<?php

namespace Tests\Selenium;

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverDimension;
use Illuminate\Foundation\Testing\TestCase as FoundationTestCase;
use Illuminate\Support\Facades\DB;
use Tests\CreatesApplication;

abstract class SeleniumBaseTestCase extends FoundationTestCase
{
    use CreatesApplication;

    const TEST_USER_OWNER = '<EMAIL>';

    const TEST_PASSWORD = 'Test_12345';

    protected RemoteWebDriver $driver;

    const HAMSTER_APOTHEKE = 'Hamster Apotheke';

    const RATHAUS_APOTHEKE = 'Rathaus Apotheke';

    const NOVA_ADMIN_USER = '<EMAIL>';

    const NOVA_ADMIN_PWD = 'pVAehbERAF';

    protected function setUp(): void
    {
        parent::setUp();

        $this->driver = self::getBrowserDriver();
        $this->driver->get(SeleniumBaseTestCase::getBaseUrl());
        $this->closeDebugModusIfApplies();
        $this->acceptCookies();
        if (! $this->isUserLoggedIn()) {
            $this->loginViaIdp();
            $this->closeChatButtonIfExists();
            //$this->closeNotificationIfExists();
            $this->showLeftMenu();
        }
    }

    /*
     * The Base URL of the page/site to test
     */
    public static function getBaseUrl(): string
    {
        return env('SELENIUM_APP_TEST_URL');
    }

    /*
     * The URL of the server where the Chrome browser driver is installed
     * - If from docker it is the name of the browser driver service name:
     *    -- "chrome" for local install
     *    -- "localhost" for GitHub Workflow
     * - if from the standalone selenium server jar file then it is the local IP Address and port that are generated
     *   when starting the selenium-server from the command line. e.g. 'http://************:4444';
     */
    public static function getServerUrlChromeDriver(): string
    {
        return env('SELENIUM_CHROME_DRIVER_BASE_SERVER_URL');
    }

    public static function getBrowserDriver($browser = 'chrome', $privateMode = true, $headless = false): RemoteWebDriver
    {
        switch ($browser) {
            case 'chrome':

                $options = new ChromeOptions;
                $capabilities = DesiredCapabilities::chrome();
                $capabilities->setCapability('acceptInsecureCerts', true);
                $capabilities->setCapability('chromeOptions', $options);

                $options = [];
                if ($privateMode) {
                    $options[] = '--incognito';
                }
                if ($headless || ! env('APP_DEBUG')) {
                    $options[] = '--headless';
                    $options[] = '--disable-gpu';
                }
                $options[] = '--window-size=975,1000';
                $args = ['args' => $options];
                $capabilities->setCapability('goog:chromeOptions', $args);

                return RemoteWebDriver::create(SeleniumBaseTestCase::getServerUrlChromeDriver(), $capabilities);

            case 'firefox':
                return RemoteWebDriver::create(SeleniumBaseTestCase::getServerUrlChromeDriver(), DesiredCapabilities::firefox());
            case 'edge':
                return RemoteWebDriver::create(SeleniumBaseTestCase::getServerUrlChromeDriver(), DesiredCapabilities::microsoftEdge());
            default:
                return RemoteWebDriver::create(SeleniumBaseTestCase::getServerUrlChromeDriver(), DesiredCapabilities::chrome());
        }
    }

    public function loginViaIdp(): void
    {
        //click the "Anmelden" link to login
        $this->driver
            ->findElement(
                WebDriverBy::xpath(
                    self::cleanHtml('//*[@id="homepage-login-link"]')
                )
            )->click();

        $this->driver
            ->findElement(WebDriverBy::id('username'))
            ->sendKeys(self::TEST_USER_OWNER);
        $this->driver
            ->findElement(WebDriverBy::id('password'))
            ->sendKeys(self::TEST_PASSWORD);

        $this->driver
            ->findElement(WebDriverBy::id('kc-login'))
            ->click();
    }

    public function loginViaKeyCloak(): void
    {
        $this->driver
            ->findElement(WebDriverBy::id('kc-login'))
            ->click();
    }

    public function loginNova(): void
    {
        $this->driver->get(SeleniumBaseTestCase::getBaseUrl().'/nova');

        try {
            $globalSearchInput = $this->driver->findElement(WebDriverBy::xpath("//input[@dusk='global-search']"));
        } catch (\Exception $e) {
            $globalSearchInput = null;
        }

        // only try to log in if not already loggeg in
        if (is_null($globalSearchInput)) {
            $this->driver
                ->findElement(WebDriverBy::id('email'))
                ->sendKeys(self::NOVA_ADMIN_USER);

            $this->driver
                ->findElement(WebDriverBy::id('password'))
                ->sendKeys(self::NOVA_ADMIN_PWD);

            $this->driver
                ->findElement(WebDriverBy::xpath("//span[text()='Anmelden']"))
                ->click();
        }
    }

    public function acceptCookies(): void
    {
        $this->driver
            ->findElement(WebDriverBy::id('cn-save'))
            ->click();
    }

    public function selectPharmacyAndAcceptTerms(string $pharmacyName): void
    {
        $selectedPharmacy = $this->driver
            ->findElement(WebDriverBy::xpath('//*[@id="homepage-menu-pharmacy-select"]/button/div/div/div/h2'));
        //if the wanted Pharmacy is already selected directly open tha details
        if (trim($selectedPharmacy->getText()) != $pharmacyName) {
            $this->driver
                ->findElement(WebDriverBy::id('homepage-menu-pharmacy-select'))
                ->click();

            sleep(1);
            $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(text(), '".$pharmacyName."')]"))
                ->click();
        }

        // check if cardLink menu visible and clickable before trying to accept the terms again
        $termsAlreadyAccepted = true;
        try {
            $this->driver
                ->findElement(WebDriverBy::id('homepage-menu-cardlink'));
        } catch (\Exception $e) {
            $termsAlreadyAccepted = false;
        }

        if ($termsAlreadyAccepted === false) {
            sleep(1);
            $this->driver
                ->findElement(WebDriverBy::id('homepage-menu-pharmacy'))
                ->click();

            // Click "Übersicht"
            sleep(1);
            $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(text(), '".$this->cleanHtml('Übersicht')."')]"))
                ->click();

            // accept terms if it is not yet accepted.
            // If it is already accepted the page with the terms is not longer visible. so for example check if the
            // accept checkbox id is present on the page
            sleep(2);

            try {
                $acceptCheckox = $this->driver
                    ->findElement(WebDriverBy::id('accepted'));
            } catch (\Exception $exception) {
                $acceptCheckox = null;
            }

            if ($acceptCheckox) {
                //checkbox terms + button
                $this->driver
                    ->findElement(WebDriverBy::id('accepted'))
                    ->click();
                sleep(3);
                $this->driver
                    ->findElement(WebDriverBy::xpath("//button[contains(text(), 'Nutzungsbedingungen akzeptieren')]"))
                    ->click();

                //checkbox data policy + button
                sleep(3);
                $this->scrollToBottom();
                $this->driver
                    ->findElement(WebDriverBy::id('data-processing-contract-checkbox'))
                    ->click();

                sleep(3);
                $this->driver
                    ->findElement(WebDriverBy::xpath("//button[contains(text(), 'Auftragsverarbeitungsvertrag akzeptieren')]"))
                    ->click();
            }
        }
    }

    /*
     * This method will book the by default selected package  ("Basis" or "Verband plus")
     */
    public function selectPharmacyAndBookContract(string $pharmacyName): void
    {
        $this->showLeftMenu();
        // Depending on the selected Pharmacy the contract type "Basis" or "Plus" would be pre-selected
        // Just go to the button "Buchungsprozess fortsetzen", the accept the terms and click the button "Kostenpflichtig buchen"
        $selectedPharmacy = $this->driver
            ->findElement(WebDriverBy::xpath('//*[@id="homepage-menu-pharmacy-select"]/button/div/div/div/h2'));
        //if the wanted Pharmacy is already selected directly open tha details
        if (trim($selectedPharmacy->getText()) != $pharmacyName) {
            $this->driver
                ->findElement(WebDriverBy::id('homepage-menu-pharmacy-select'))
                ->click();

            sleep(1);
            $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(text(), '".$pharmacyName."')]"))
                ->click();
        }
        // close the left menu if case (just clicking on top right to ensure that it is closed)
        $this->closeLeftMenu();

        // Only try to activate if not already activated.  => check if you are on the Booking page
        if ($this->isTextDisplayedOnPage('Mitgliedschaft buchen')) {
            $this->scrollToBottom();
            $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(normalize-space(text()), 'Buchungsprozess fortsetzen')]"))
                ->click();

            $this->scrollToBottom();

            //Accept the  2 terms check boxes
            $this->driver
                ->findElement(WebDriverBy::id('acceptedTerms'))
                ->click();
            $this->driver
                ->findElement(WebDriverBy::id('acceptedDataContract'))
                ->click();

            $this->driver->executeScript('window.scrollBy(0, 300);');

            $this->scrollToBottom();
            $contentDiv = $this->driver->findElement(WebDriverBy::id('content-wrapper'));
            $this->driver->executeScript('arguments[0].scrollTop = arguments[0].scrollHeight;', [$contentDiv]);

            sleep(3);
            $this->driver
                ->findElement(WebDriverBy::xpath("//button[contains(normalize-space(), 'Kostenpflichtig buchen')]"))
                ->click();

            sleep(6);
        }
    }

    public function scrollToBottom(): void
    {
        $this->driver
            ->executeScript('window.scrollTo(0, document.body.scrollHeight);');
    }

    public function logout(): void
    {
        $logoutLink = $this->driver
            ->findElement(WebDriverBy::id('logout-button'));

        $this->driver->executeScript('arguments[0].scrollIntoView(true);', [$logoutLink]);
        //click the "Logout" link to log out

        sleep(1);
        $logoutLink->click();
    }

    public function closeDebugModusIfApplies(): void
    {
        try {
            $debugBarCloseButton = $this->driver
                ->findElement(WebDriverBy::className('phpdebugbar-close-btn'));
        } catch (\Exception $e) {
            $debugBarCloseButton = null;
        }
        if ($debugBarCloseButton) {
            $debugBarCloseButton->click();
        }
    }

    public static function cleanHtml(string $str): ?string
    {
        $str = str_replace(["\r", "\n"], '', $str);
        $str = preg_replace('!\s+!', ' ', $str);

        return $str;
    }

    public static function getFailuresScreenshotsPath(): mixed
    {
        return env('SELENIUM_FAILURES_SCREENSHOTS_PATH');
    }

    public static function getFailuresVideoPath(): mixed
    {
        return env('SELENIUM_FAILURES_VIDEOS_PATH');
    }

    public function showLeftMenu(): void
    {
        // Assert if the left menu is not already shown before clicking
        sleep(1);
        if (! $this->isLeftMenuVisible()) {
            $this->driver->findElement(
                WebDriverBy::xpath('/html/body/div[4]/div[1]/nav/div/div/div[2]/button')
            )->click();
        }

        sleep(1);
    }

    public function isLeftMenuVisible(): bool
    {
        // Just check if the Dashboard Menu is there as it is available for all user roles
        if (
            $this->driver
                ->findElement(WebDriverBy::xpath('//*[@id="homepage-menu-dashboard"]'))
                ->isDisplayed()) {
            return true;
        }

        return false;
    }

    public function closeChatButtonIfExists(): void
    {
        //To close the modal just click anywhere on the remaining space on the page
        $this->driver
            ->executeScript('document.elementFromPoint(1, 1).click();');
    }

    public function closeLeftMenu(): void
    {
        //To close the modal just click anywhere on the remaining space on the page
        $this->driver
            ->executeScript('
            var x = window.innerWidth - 1;
            var y = 1;
            var element = document.elementFromPoint(x, y);
            if (element) element.click();
        ');
    }

    /**
     * This will close opened Modals, overlays and dropdowns
     */
    public function clickEmptyClick(): void
    {
        //To close the modal just click anywhere on the remaining space on the page
        $this->driver
            ->executeScript('document.elementFromPoint(1, 1).click();');
    }

    public function closeNotificationIfExists(): void
    {
        $this->driver
            ->findElement(WebDriverBy::xpath('/html/body/div[4]/div/div/div/div/div/div[3]/button'))
            ->click();
    }

    public function isUserLoggedIn(): bool
    {
        try {
            $userIconActive = $this->driver
                ->findElement(WebDriverBy::xpath('//button[contains(@aria-label, "Main menu")]'));
        } catch (\Exception $exception) {
            $userIconActive = null;
        }

        try {
            $loginButton = $this->driver
                ->findElement(WebDriverBy::id('homepage-login-link'));
        } catch (\Exception $exception) {
            $loginButton = null;
        }

        if (
            ! is_null($userIconActive) &&
            $userIconActive->isDisplayed() &&
            is_null($loginButton)
        ) {
            return true;
        }

        return false;
    }

    public function seeTextOnPage(string $text): void
    {
        $this->assertTrue(
            $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(normalize-space(text()), '".$text."')]"))
                ->isDisplayed()
        );
    }

    public function seeTextInsideElement(string $element, string $text): void
    {
        $this->assertTrue(
            $this->driver
                ->findElement(
                    WebDriverBy::xpath($element.'[contains(., "'.$text.'")]')
                )->isDisplayed()
        );
    }

    public function seeLinkAndHref(string $href, string $linkText): void
    {
        $this->assertEquals(
            $href,
            $this->driver
                ->findElement(
                    WebDriverBy::linkText($linkText)
                )
                ->getAttribute('href'),
            'The Expected '.$linkText.' URL is not correct!'
        );
    }

    public function isTextDisplayedOnPage(string $text): bool
    {
        try {
            $textElement = $this->driver
                ->findElement(WebDriverBy::xpath("//*[contains(text(), '".$text."')]"));
        } catch (\Exception $exception) {
            $textElement = null;
        }

        if (is_null($textElement)) {
            return false;
        }

        return $textElement->isEnabled();
    }

    /**
     * This method will only work on local systems. But not in the GitHub Pipeline because the docker test instances
     * databases are not accessible from the GitHub network. So Use the method novaDeleteExistingCardLinks() instead
     * But this method can still be used locally when developing in order to speed up the execution time
     *
     * @deprecated use the method novaDeleteExistingCardLinks() instead
     */
    public function truncateTable(string $tableName): void
    {
        DB::connection('mysql')->table($tableName)->truncate();

        // Verify the table is empty
        $cardLinks = DB::connection('mysql')->table($tableName)->get();
        $this->assertCount(0, $cardLinks, 'Successfully deleted the table '.$tableName);
    }

    public function novaDeleteExistingCardLinks(): void
    {
        //Resize to be able to click easily
        sleep(5);
        $this->driver
            ->manage()
            ->window()
            ->setSize(new WebDriverDimension(1100, 1000));

        $this->driver
            ->findElement(WebDriverBy::xpath("(//span[text()='Bestellungen'])[1]"))
            ->click();
        sleep(7);

        try {
            $noEntryText = $this->driver
                ->findElement(
                    WebDriverBy::xpath("//h3[text()='Keine Ressource entspricht den angegebenen Kriterien.']")
                );
        } catch (\Exception $exception) {
            $noEntryText = null;
        }

        if (is_null($noEntryText)) {
            // Select and delete only the reserved and ordered and activating CardLinks
            // (as the activated ones cannot be deleted anymore)
            $this->driver
                ->findElement(WebDriverBy::xpath('//div[@dusk="filter-selector"]'))
                ->click();

            $this->driver
                ->findElement(WebDriverBy::xpath('//label[@dusk="Status-boolean-filter-Reserved-option"]'))
                ->click();

            $this->driver
                ->findElement(WebDriverBy::xpath('//label[@dusk="Status-boolean-filter-Ordered-option"]'))
                ->click();

            $this->driver
                ->findElement(WebDriverBy::xpath('//label[@dusk="Status-boolean-filter-Activating-option"]'))
                ->click();
            $this->clickEmptyClick();

            // Only try to select if there is at least 1 match
            if (! $this->isTextDisplayedOnPage('Keine Ressource entspricht den angegebenen Kriterien.')) {
                $this->driver
                    ->findElement(WebDriverBy::xpath('//button[@dusk="select-all-dropdown-trigger"]'))
                    ->click();

                $this->driver
                    ->findElement(WebDriverBy::xpath('//div[@dusk="select-all-matching-button"]'))
                    ->click();

                $this->driver
                    ->findElement(WebDriverBy::xpath('//div[@dusk="delete-menu"]'))
                    ->click();

                /*
                $this->driver
                    ->findElement(WebDriverBy::xpath("//button[@dusk='force-delete-selected-button']"))
                    ->click();
                */
                $this->driver
                    ->findElement(WebDriverBy::xpath("//button[@dusk='delete-selected-button']"))
                    ->click();

                $this->driver
                    ->findElement(WebDriverBy::xpath("//button[@dusk='confirm-delete-button']"))
                    ->click();

                sleep(3);

                //check that the CardLik have been deleted
                $this->seeTextOnPage('Keine Ressource entspricht den angegebenen Kriterien.');
            }
        }

        $this->driver
            ->manage()
            ->window()
            ->setSize(new WebDriverDimension(975, 1000));
    }

    public function onNotSuccessfulTest(\Throwable $t): never
    {
        $screenshotFile = SeleniumBaseTestCase::getFailuresScreenshotsPath().'/'.
            date('Y-m-d H:i:s').'__'.class_basename(static::class).'::'.$this->name().'()__.png';
        $this->driver->takeScreenshot($screenshotFile);
        parent::onNotSuccessfulTest($t);
    }

    public function __destruct()
    {
        //Kill the session
        $this->driver->quit();
    }
}
