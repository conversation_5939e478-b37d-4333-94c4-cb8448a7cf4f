<?php

namespace Tests\helper;

use App\BillingAddress;
use App\Invoice;
use App\User;
use Illuminate\Database\Eloquent\Collection;

trait InvoiceHelper
{
    public function createInvoice(): Invoice
    {
        return $this->createInvoices(1)->first();
    }

    public function createInvoices(int $count, ?BillingAddress $billingAddress = null, ?int $userId = null): Collection
    {
        return Invoice::factory($count)->create([
            'billing_address_id' => $billingAddress ?? $this->createBillingAddress($userId),
        ]);
    }

    public function createBillingAddress($userId = null): BillingAddress
    {
        return BillingAddress::factory()->create([
            'user_id' => $userId ?? User::factory()->create(),
        ]);
    }
}
