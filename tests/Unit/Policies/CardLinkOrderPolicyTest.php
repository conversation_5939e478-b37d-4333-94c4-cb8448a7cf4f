<?php

namespace Tests\Unit\Policies;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PharmacyRoleEnum;
use App\Helper\PharmacySessionHelper;
use App\Policies\CardLinkOrderPolicy;
use App\User;
use Tests\TestCase;

class CardLinkOrderPolicyTest extends TestCase
{
    public function test_view_any_no_owner_or_sub_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->viewAny(new User));
        $this->assertFalse($policy->viewAny($employee));
        $this->assertFalse($policy->viewAny($branchManager));
    }

    public function test_view_any_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->viewAny($user));
    }

    public function test_view_any_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->declineTermsForPharmacy($pharmacy);
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->viewAny($user));
        $this->assertFalse($policy->viewAny($subOwner));
    }

    public function test_view_any_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->viewAny($user));
        $this->assertFalse($policy->viewAny($subOwner));
    }

    public function test_view_any_ok(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertTrue($policy->viewAny($user));
        $this->assertTrue($policy->viewAny($subOwner));
    }

    public function test_view_no_owner_or_sub_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->view(new User, $cardLinkOrder));
        $this->assertFalse($policy->view($employee, $cardLinkOrder));
        $this->assertFalse($policy->view($branchManager, $cardLinkOrder));
    }

    public function test_view_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->view($user, $this->createCardLinkOrder(1, $user->id)));
    }

    public function test_view_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->generalSettings()->forceDelete();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->view($user, $cardLinkOrder));
        $this->assertFalse($policy->view($subOwner, $cardLinkOrder));
    }

    public function test_view_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->view($user, $cardLinkOrder));
        $this->assertFalse($policy->view($subOwner, $cardLinkOrder));
    }

    public function test_view_ok(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);

        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertTrue($policy->view($user, $cardLinkOrder));
        $this->assertTrue($policy->view($subOwner, $cardLinkOrder));
    }

    public function test_create_no_owner_or_sub_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->create(new User));
        $this->assertFalse($policy->create($employee));
        $this->assertFalse($policy->create($branchManager));
    }

    public function test_create_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->create($user));
    }

    public function test_create_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->declineTermsForPharmacy($pharmacy);
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->create($user));
        $this->assertFalse($policy->create($subOwner));
    }

    public function test_create_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->create($user));
        $this->assertFalse($policy->create($subOwner));
    }

    public function test_create_ok(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertTrue($policy->create($user));
        $this->assertTrue($policy->create($subOwner));
    }

    public function test_update_no_owner_or_sub_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->update(new User, $cardLinkOrder));
        $this->assertFalse($policy->update($employee, $cardLinkOrder));
        $this->assertFalse($policy->update($branchManager, $cardLinkOrder));
    }

    public function test_update_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->update($user, $this->createCardLinkOrder(1, $user->id)));
    }

    public function test_update_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->declineTermsForPharmacy($pharmacy);
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->update($user, $cardLinkOrder));
        $this->assertFalse($policy->update($subOwner, $cardLinkOrder));
    }

    public function test_update_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->update($user, $cardLinkOrder));
        $this->assertFalse($policy->update($subOwner, $cardLinkOrder));
    }

    public function test_update_ok(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $cardLinkOrder = $this->createCardLinkOrder($pharmacy->id, $user->id);

        $this->actingAs($user);

        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertTrue($policy->update($user, $cardLinkOrder));
        $this->assertTrue($policy->update($subOwner, $cardLinkOrder));
    }

    public function test_access_no_owner_or_sub_owner(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $branchManager = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->access(new User));
        $this->assertFalse($policy->access($employee));
        $this->assertFalse($policy->access($branchManager));
    }

    public function test_access_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->access($user));
    }

    public function test_access_no_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->declineTermsForPharmacy($pharmacy);
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->access($user));
        $this->assertFalse($policy->access($subOwner));
    }

    public function test_access_no_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);

        $this->actingAs($user);
        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertFalse($policy->access($user));
        $this->assertFalse($policy->access($subOwner));
    }

    public function test_access_ok(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $subOwner = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());
        $this->actingAs($user);

        PharmacySessionHelper::set($pharmacy);

        $policy = new CardLinkOrderPolicy;

        $this->assertTrue($policy->access($user));
        $this->assertTrue($policy->access($subOwner));
    }
}
