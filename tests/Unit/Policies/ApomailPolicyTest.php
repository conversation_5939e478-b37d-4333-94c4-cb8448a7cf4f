<?php

namespace Tests\Unit\Policies;

use App\BlocklistItem;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PermissionEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\Policies\ApomailPolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApomailPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_view_any(): void
    {
        $owner = $this->createPharmacyUser();

        $this->assertFalse((new ApomailPolicy)->viewAny($owner));

        [$subOwner, $pharmacy] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $this->assertFalse((new ApomailPolicy)->viewAny($subOwner));

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->assertTrue((new ApomailPolicy)->viewAny($user));
    }

    public function test_association_user_cannot_view_apomail_administration(): void
    {
        [$user] = $this->createAssociationUser();

        $this->actingAs($user);

        $this->assertFalse((new ApomailPolicy)->viewAdministration($user));
    }

    public function test_user_without_pharmacies_cannot_view_apomail_administration(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user);

        $this->assertFalse((new ApomailPolicy)->viewAdministration($user));
    }

    public function test_pharmacy_without_subscription_cannot_view_administration(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->assertFalse((new ApomailPolicy)->viewAdministration($user, $pharmacy));
    }

    public function test_pharmacy_with_subscription_can_view_administration(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, [BaseStripeProduct::make()]);

        $this->assertTrue((new ApomailPolicy)->viewAdministration($user, $pharmacy));
    }

    public function test_create_as_employee(): void
    {
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $employeeGood = $this->createEmployeeWithApomailUser($pharmacy, '<EMAIL>')[1];

        $this->assertTrue((new ApomailPolicy)->createAsEmployee($employeeGood));
    }

    public function test_create_as_employee_blacklisted_email(): void
    {
        BlocklistItem::create(['word' => 'badword']);
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $employeeBad = $this->createEmployeeWithApomailUser($pharmacy, '<EMAIL>')[1];

        $this->assertFalse((new ApomailPolicy)->createAsEmployee($employeeBad));
    }

    public function test_support_has_no_general_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_support_has_read_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new ApomailPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_support_has_no_write_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_support_has_no_delete_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_support_has_no_restore_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_operation_has_no_general_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operation_has_read_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new ApomailPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operation_has_no_write_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operation_has_no_delete_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operation_has_no_restore_access(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new ApomailPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }
}
