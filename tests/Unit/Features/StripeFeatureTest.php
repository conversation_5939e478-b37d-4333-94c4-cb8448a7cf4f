<?php

namespace tests\Unit\Features;

use App\Enums\FeatureScope;
use App\Features\StripeFeature;
use App\Settings\SubscriptionSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Pennant\Feature;
use Tests\TestCase;

class StripeFeatureTest extends TestCase
{
    use RefreshDatabase;

    public function test_resolves_true(): void
    {
        $settings = app(SubscriptionSettings::class);
        $settings->stripeEnabledAt = now();
        $settings->save();

        $this->assertTrue(Feature::for(FeatureScope::App->value)->active(StripeFeature::class));
        $this->assertTrue(Feature::active(StripeFeature::class));
    }

    public function test_resolves_false(): void
    {
        $settings = app(SubscriptionSettings::class);
        $settings->stripeEnabledAt = now()->addDay();
        $settings->save();

        $this->assertFalse(Feature::for(FeatureScope::App->value)->active(StripeFeature::class));
        $this->assertFalse(Feature::active(StripeFeature::class));
    }
}
