<?php

namespace Tests\Unit\Actions;

use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use App\InhalationTechniquePatient;
use App\MeasureBloodPressurePatient;
use App\PharmaceuticalService;
use App\PharmaceuticalServicePatient;
use Tests\TestCase;

class AnonymizePharmaceuticalServiceActionTest extends TestCase
{
    public function test_it_anonymizes_pharmaceutical_service_inhalaton(): void
    {

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        /** @var PharmaceuticalService $pharmaceuticalService */
        $pharmaceuticalService = $pharmacy->pharmaceuticalServices()->create([
            'association_id' => $pharmacy->owner()?->pharmacyProfile?->association_id,
            'user_id' => $user->id,
            'date' => now()->startOfDay(),
            'type' => PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE,
        ]);

        $pharmaceuticalService->pharmaceuticalServicePatient()->create();
        $pharmaceuticalService->measureBloodPressurePatient()->create();

        app(\App\Actions\AnonymizePharmaceuticalServiceAction::class)->execute($pharmaceuticalService);

        $this->assertDatabaseCount((new MeasureBloodPressurePatient)->getTable(), 0);
        $this->assertDatabaseCount((new PharmaceuticalServicePatient)->getTable(), 0);

    }

    public function test_it_anonymizes_pharmaceutical_service(): void
    {

        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        /** @var PharmaceuticalService $pharmaceuticalService */
        $pharmaceuticalService = $pharmacy->pharmaceuticalServices()->create([
            'association_id' => $pharmacy->owner()?->pharmacyProfile?->association_id,
            'user_id' => $user->id,
            'date' => now()->startOfDay(),
            'type' => PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE,
        ]);

        $pharmaceuticalService->pharmaceuticalServicePatient()->create();
        $pharmaceuticalService->measureBloodPressurePatient()->create();

        app(\App\Actions\AnonymizePharmaceuticalServiceAction::class)->execute($pharmaceuticalService);

        $this->assertDatabaseCount((new InhalationTechniquePatient)->getTable(), 0);
        $this->assertDatabaseCount((new PharmaceuticalServicePatient)->getTable(), 0);

    }
}
