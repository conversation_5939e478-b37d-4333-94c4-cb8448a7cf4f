<?php

namespace Tests\Unit\Actions\TokenService;

use App\Actions\TokenService\SynchronizeUserPermissionsInTokenServiceAction;
use App\Jobs\TokenService\SynchronizeUserPermissionsInTokenServiceJob;
use App\User;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

/**
 * @group TokenService
 */
class SynchronizeUserPermissionsInTokenServiceActionTest extends TestCase
{
    public function test_job_not_dispatched_for_non_pharmacy_user(): void
    {
        $this->clearExistingHttpFakes();

        Queue::fake();

        app(SynchronizeUserPermissionsInTokenServiceAction::class)->execute(User::factory()->create());
        app(SynchronizeUserPermissionsInTokenServiceAction::class)->execute($this->createAssociationUser()[0]);

        Queue::assertPushed(SynchronizeUserPermissionsInTokenServiceJob::class, 0);
    }

    public function test_job_dispatched_for_pharmacy_user(): void
    {
        $this->clearExistingHttpFakes();

        Queue::fake();

        app(SynchronizeUserPermissionsInTokenServiceAction::class)->execute($this->createPharmacyUser());

        Queue::assertPushed(SynchronizeUserPermissionsInTokenServiceJob::class, 1);
    }
}
