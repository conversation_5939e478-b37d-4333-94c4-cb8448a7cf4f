<?php

namespace Tests\Unit\Http\Integrations\Ia\UpdatePharmacyDataAPI\Requests;

use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\AddressDto;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Dto\PharmacyDto;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdatePharmacyDetailsRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * @group PushPharmacyDataToIA
 */
class UpdatePharmacyDetailsRequestTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_sets_the_correct_endpoint(): void
    {
        $pharmacyDTO = new PharmacyDto;
        $pharmacyDTO->address = new AddressDto;

        $request = new UpdatePharmacyDetailsRequest($pharmacyDTO);

        $this->assertEquals('/update-pharmacy-details', $request->resolveEndpoint());
    }

    public function test_it_sets_the_correct_body(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacyDTO = new PharmacyDto;
        $pharmacyDTO->address = new AddressDto;
        $pharmacyDTO->pharmacyId = (int) $pharmacy->id;
        $pharmacyDTO->address->street = 'Street';
        $pharmacyDTO->address->zip = 'Zip';
        $pharmacyDTO->partnerId = 'PartnerId';
        $pharmacyDTO->commercialRegister = 'CommercialRegister';
        $pharmacyDTO->facebook = 'Facebook';
        $pharmacyDTO->instagram = 'Instagram';
        $pharmacyDTO->owner = $pharmacy->owner()?->name ?? '';
        $pharmacyDTO->website = 'Website';
        $pharmacyDTO->calendarUrl = 'CalendarUrl';

        $request = new UpdatePharmacyDetailsRequest($pharmacyDTO);

        $expectedBody = [
            'pharmacyId' => 'PartnerId',
            'commercialRegister' => [
                'name' => 'CommercialRegister',
            ],
            'address' => [
                'poBox' => [
                    'zip' => 'Zip',
                    'address' => [
                        'Street',
                    ],
                ],
            ],
            'socialMedia' => [
                'facebook' => [
                    'url' => 'Facebook',
                ],
                'instagram' => [
                    'url' => 'Instagram',
                ],
            ],
            'owners' => [
                [
                    'fullName' => $user->name,
                ],
            ],
            'tools' => [
                'website' => 'Website',
                'reservation' => 'CalendarUrl',
            ],
        ];

        $this->assertEquals($expectedBody, $request->defaultBody());
    }
}
