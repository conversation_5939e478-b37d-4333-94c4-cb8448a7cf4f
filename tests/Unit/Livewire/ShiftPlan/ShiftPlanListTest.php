<?php

namespace Tests\Unit\Livewire\ShiftPlan;

use App\Enums\PharmacyRoleEnum;
use App\Livewire\ShiftPlan\ShiftPlanList;
use App\ShiftPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

/** @group shiftplan */
class ShiftPlanListTest extends TestCase
{
    use RefreshDatabase;

    public function test_only_shift_plans_belonging_to_the_owner_or_pharmacy_are_visible(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        $shiftPlanForOwner = ShiftPlan::factory()->create([
            'owner_id' => $owner->id,
        ]);

        $shiftPlanForEmployee = ShiftPlan::factory()->create([
            'owner_id' => $employee->id,
        ]);

        $shiftPlanForOther = ShiftPlan::factory()->create();

        $this->actingAs($owner);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForOwner->name)
            ->assertDontSee($shiftPlanForEmployee->name)
            ->assertDontSee($shiftPlanForOther->name);

        $this->markTestSkipped('Employee should not see any shift plans');
        $this->actingAs($employee);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForEmployee->name)
            ->assertDontSee($shiftPlanForOwner->name)
            ->assertDontSee($shiftPlanForOther->name);
    }

    public function test_different_owner_and_employee_cannot_see_each_others_shift_plans(): void
    {
        [$ownerA, $pharmacyA] = $this->createPharmacyUserWithPharmacy();
        $employeeA = $this->createPharmacyEmployee($pharmacyA, PharmacyRoleEnum::EMPLOYEE);

        [$ownerB, $pharmacyB] = $this->createPharmacyUserWithPharmacy();
        $employeeB = $this->createPharmacyEmployee($pharmacyB, PharmacyRoleEnum::EMPLOYEE);

        $shiftPlanForOwnerA = ShiftPlan::factory()->create([
            'owner_id' => $ownerA->id,
        ]);

        $shiftPlanForEmployeeA = ShiftPlan::factory()->create([
            'owner_id' => $employeeA->id,
        ]);

        $shiftPlanForOwnerB = ShiftPlan::factory()->create([
            'owner_id' => $ownerB->id,
        ]);

        $shiftPlanForEmployeeB = ShiftPlan::factory()->create([
            'owner_id' => $employeeB->id,
        ]);

        $this->actingAs($ownerA);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForOwnerA->name)
            ->assertDontSee($shiftPlanForEmployeeA->name)
            ->assertDontSee($shiftPlanForOwnerB->name)
            ->assertDontSee($shiftPlanForEmployeeB->name);

        $this->markTestSkipped('Employee should not see any shift plans');

        $this->actingAs($employeeA);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForEmployeeA->name)
            ->assertDontSee($shiftPlanForOwnerA->name)
            ->assertDontSee($shiftPlanForOwnerB->name)
            ->assertDontSee($shiftPlanForEmployeeB->name);

        $this->actingAs($ownerB);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForOwnerB->name)
            ->assertDontSee($shiftPlanForEmployeeB->name)
            ->assertDontSee($shiftPlanForOwnerA->name)
            ->assertDontSee($shiftPlanForEmployeeA->name);

        $this->actingAs($employeeB);

        Livewire::test(ShiftPlanList::class)
            ->assertSee($shiftPlanForEmployeeB->name)
            ->assertDontSee($shiftPlanForOwnerB->name)
            ->assertDontSee($shiftPlanForOwnerA->name)
            ->assertDontSee($shiftPlanForEmployeeA->name);
    }
}
