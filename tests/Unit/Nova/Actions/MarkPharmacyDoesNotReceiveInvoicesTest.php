<?php

namespace Tests\Unit\Nova\Actions;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Nova\Actions\MarkPharmacyDoesNotReceiveInvoices;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Cashier\Cashier;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class MarkPharmacyDoesNotReceiveInvoicesTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_updates_the_subscription(): void
    {
        $pharmacy = $this->createPharmacy();

        $subscription = $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->assertTrue($pharmacy->send_invoices);

        $action = new MarkPharmacyDoesNotReceiveInvoices;

        $action->handle(new ActionFields(collect(), collect()), collect([$pharmacy]));

        $this->assertFalse($pharmacy->send_invoices);

        $stripeSubscription = Cashier::stripe()->subscriptions->retrieve($subscription->stripe_id);

        $this->assertEquals('keep_as_draft', $stripeSubscription->pause_collection->behavior);
    }
}
