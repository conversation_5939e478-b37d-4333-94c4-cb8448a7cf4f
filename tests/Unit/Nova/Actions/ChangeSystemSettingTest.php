<?php

namespace Tests\Unit\Nova\Actions;

use App\Nova\Actions\ChangeSystemSetting;
use App\SystemSetting;
use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Request;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\KeyValue;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Tests\Support\TestSettings;
use Tests\TestCase;

class ChangeSystemSettingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->actingAs($this->createStaff(), 'staff');

        $testData = TestSettings::getTestData();

        foreach ($testData as $propertyName => $initialValue) {
            SystemSetting::create([
                'group' => TestSettings::group(),
                'name' => $propertyName,
                'payload' => json_encode($initialValue),
            ]);
        }

        Config::set('settings.settings', [TestSettings::class]);
    }

    public function test_auto_discover_is_off(): void
    {
        $this->assertEmpty(config('settings.auto_discover_settings'));
    }

    public function test_array_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $this->executeHandle($systemSetting, []);
        $this->assertSame([], app(TestSettings::class)->array);

        $this->executeHandle($systemSetting, ['1', 2, 3.45]);
        $this->assertSame(['1', 2, 3.45], app(TestSettings::class)->array);
    }

    public function test_array_type_with_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, 'str');
    }

    public function test_array_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_array_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, 1);
    }

    public function test_array_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, true);
    }

    public function test_array_null_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array_null');

        $this->executeHandle($systemSetting, []);
        $this->assertSame([], app(TestSettings::class)->array_null);

        $this->executeHandle($systemSetting, ['1', 2, 3.45]);
        $this->assertSame(['1', 2, 3.45], app(TestSettings::class)->array_null);
    }

    public function test_array_null_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array_null');

        $this->executeHandle($systemSetting, null);
        $this->assertNull(app(TestSettings::class)->array_null);
    }

    public function test_string_type_with_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $this->executeHandle($systemSetting, 'str');
        $this->assertSame('str', app(TestSettings::class)->string);
    }

    public function test_string_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_string_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, []);
    }

    public function test_string_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $this->executeHandle($systemSetting, true);
        $this->assertSame('1', app(TestSettings::class)->string);
    }

    public function test_string_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $this->executeHandle($systemSetting, 123);
        $this->assertSame('123', app(TestSettings::class)->string);
    }

    public function test_string_null_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string_null');

        $this->executeHandle($systemSetting, null);
        $this->assertNull(app(TestSettings::class)->string_null);
    }

    public function test_integer_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $this->executeHandle($systemSetting, 123);
        $this->assertSame(123, app(TestSettings::class)->integer);
    }

    public function test_integer_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_integer_type_with_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, 'str');
    }

    public function test_integer_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, []);
    }

    public function test_integer_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $this->executeHandle($systemSetting, true);
        $this->assertSame(1, app(TestSettings::class)->integer);
    }

    public function test_integer_null_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer_null');

        $this->executeHandle($systemSetting, null);
        $this->assertNull(app(TestSettings::class)->integer_null);
    }

    public function test_float_type_with_float(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->executeHandle($systemSetting, 1.23);
        $this->assertSame(1.23, app(TestSettings::class)->float);
    }

    public function test_float_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->executeHandle($systemSetting, 123);
        $this->assertSame(123.0, app(TestSettings::class)->float);
    }

    public function test_float_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_float_type_with_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, 'str');
    }

    public function test_float_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, []);
    }

    public function test_float_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $this->executeHandle($systemSetting, true);
        $this->assertSame(1.0, app(TestSettings::class)->float);
    }

    public function test_float_null_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float_null');

        $this->executeHandle($systemSetting, null);
        $this->assertNull(app(TestSettings::class)->float_null);
    }

    public function test_boolean_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $this->executeHandle($systemSetting, true);
        $this->assertSame(true, app(TestSettings::class)->boolean);
    }

    public function test_boolean_type_with_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $this->executeHandle($systemSetting, 'str');
        $this->assertSame(true, app(TestSettings::class)->boolean);
    }

    public function test_boolean_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $this->executeHandle($systemSetting, 1);
        $this->assertSame(true, app(TestSettings::class)->boolean);

        $this->executeHandle($systemSetting, 2);
        $this->assertSame(true, app(TestSettings::class)->boolean);

        $this->executeHandle($systemSetting, 0);
        $this->assertSame(false, app(TestSettings::class)->boolean);
    }

    public function test_boolean_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, []);
    }

    public function test_boolean_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_carbon_type_with_carbon(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $now = Carbon::now()->startOfMinute();
        $this->executeHandle($systemSetting, $now);
        $this->assertTrue(app(TestSettings::class)->carbon->equalTo($now));
    }

    public function test_carbon_type_with_date_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $now = Carbon::now()->startOfDay();
        $this->executeHandle($systemSetting, $now->toDateString());
        $this->assertTrue(app(TestSettings::class)->carbon->equalTo($now));
    }

    public function test_carbon_type_with_random_string(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->expectException(InvalidFormatException::class);
        $this->executeHandle($systemSetting, 'str');
    }

    public function test_carbon_type_with_empty_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->executeHandle($systemSetting, []);
        $this->assertInstanceOf(Carbon::class, app(TestSettings::class)->carbon);
    }

    public function test_carbon_type_with_array(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, [1, 2, 3]);
    }

    public function test_carbon_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->expectException(\TypeError::class);
        $this->executeHandle($systemSetting, null);
    }

    public function test_carbon_type_with_integer(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->executeHandle($systemSetting, 123);
        $this->assertInstanceOf(Carbon::class, app(TestSettings::class)->carbon);
    }

    public function test_carbon_type_with_boolean(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $this->expectException(InvalidFormatException::class);
        $this->executeHandle($systemSetting, true);
    }

    public function test_carbon_null_type_with_null(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon_null');

        $this->executeHandle($systemSetting, null);
        $this->assertNull(app(TestSettings::class)->carbon_null);
    }

    public function test_array_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('array');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof KeyValue);
    }

    public function test_string_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('string');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof Text);
    }

    public function test_integer_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('integer');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof Number);
    }

    public function test_float_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('float');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof Number);
    }

    public function test_boolean_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('boolean');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof Boolean);
    }

    public function test_carbon_field(): void
    {
        $systemSetting = $this->getSystemSettingByPropertyName('carbon');

        $field = $this->getField($systemSetting);
        $this->assertTrue($field instanceof DateTime);
    }

    private function executeHandle(SystemSetting $systemSetting, mixed $testValue): void
    {
        Request::merge(['resourceId' => $systemSetting->id]);

        $fields = new ActionFields(collect([
            'payload' => $testValue,
        ]), collect());
        $novaAction = new ChangeSystemSetting;

        $novaAction->handle($fields);
    }

    private function getSystemSettingByPropertyName(string $propertyName): SystemSetting
    {
        /** @var SystemSetting $systemSetting */
        $systemSetting = SystemSetting::query()
            ->where('group', TestSettings::group())
            ->where('name', $propertyName)
            ->first();

        return $systemSetting;
    }

    private function getField(SystemSetting $systemSetting): Field
    {
        Request::merge(['resourceId' => $systemSetting->id]);
        $novaAction = new ChangeSystemSetting;
        $fields = $novaAction->fields(new NovaRequest);

        return $fields[2];
    }
}
