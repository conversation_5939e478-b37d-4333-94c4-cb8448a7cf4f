<?php

namespace Tests\Unit\Console\Commands;

use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\PharmacyAddress;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GenerateUuidForAllUsersTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_generates_uuids()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $employee = $this->createPharmacyEmployee($pharmacy);
        $pharmacy2 = Pharmacy::factory()->create();
        PharmacyAddress::factory()->create([
            'pharmacy_id' => $pharmacy2->id,
        ]);
        $pharmacy2->assignUser($user, PharmacyRoleEnum::OWNER);
        $employee2 = $this->createPharmacyEmployee($pharmacy2);
        [$user2, $pharmacy3] = $this->createPharmacyUserWithPharmacy();
        $employee3 = $this->createPharmacyEmployee($pharmacy3);

        User::query()->update(['organisation_uuid' => null]);

        $this->travel(5)->minutes();

        $this->artisan('users:generate-organisation-uuid');

        $this->assertDatabaseHas('Users', ['organisation_uuid' => null]);

        $user->refresh();
        $user2->refresh();
        $employee->refresh();
        $employee2->refresh();
        $employee3->refresh();

        $this->assertSame($user->organisation_uuid, $employee->organisation_uuid);
        $this->assertSame($user->organisation_uuid, $employee2->organisation_uuid);
        $this->assertNotSame($user2->organisation_uuid, $employee->organisation_uuid);
        $this->assertSame($user2->organisation_uuid, $employee3->organisation_uuid);
    }
}
