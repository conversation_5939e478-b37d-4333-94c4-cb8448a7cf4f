<?php

namespace Tests\Unit\Models;

use App\CalendarTopic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CalendarTopicTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_belongs_to_group(): void
    {
        $calendarTopic = CalendarTopic::all()->random();

        $this->assertNotNull($calendarTopic->calendarTopicGroup);
    }

    public function test_it_fetches_only_public_entries(): void
    {
        $calendarTopics = CalendarTopic::all();

        $count = $calendarTopics->count();

        $calendarTopic = $calendarTopics->random();
        $calendarTopic->update(['is_public' => false]);

        $this->assertCount($count - 1, CalendarTopic::public()->get());
    }

    public function test_it_changes_status_of_calendar_topic(): void
    {
        $calendarTopics = CalendarTopic::all();

        $count = $calendarTopics->count();

        $calendarTopic = $calendarTopics->random();
        $calendarTopic->deactivate();

        $this->assertFalse($calendarTopic->refresh()->isPublic());
        $this->assertCount($count - 1, CalendarTopic::public()->get());

        $calendarTopic->activate();

        $this->assertTrue($calendarTopic->isPublic());
        $this->assertCount($count, CalendarTopic::public()->get());
    }
}
