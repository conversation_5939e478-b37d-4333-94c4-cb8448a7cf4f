<?php

namespace Tests\Unit\Models;

use App\BusinessHour;
use App\Pharmacy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BusinessHourTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_touches_the_parent_pharmacy()
    {
        $pharmacy = Pharmacy::factory()->create();

        $this->travel(5)->minutes();

        BusinessHour::factory()->create([
            'pharmacy_id' => $pharmacy->id,
        ]);

        $pharmacy2 = Pharmacy::first();

        $this->assertNotEquals($pharmacy->updated_at->format('U'), $pharmacy2->updated_at->format('U'));
    }
}
