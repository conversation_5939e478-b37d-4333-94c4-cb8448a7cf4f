<?php

namespace Tests\Unit\Traits;

use App\BrochureCode;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\SubscriptionOrder;
use App\UserPharmacyProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscribableTest extends TestCase
{
    use RefreshDatabase;

    public function test_has_subscriptions()
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();
        $this->assertCount(0, $subscribable->oldSubscriptions);

        $subscribable->oldSubscriptions()->save($this->createSubscription());
        $this->assertCount(1, $subscribable->refresh()->oldSubscriptions);
    }

    public function test_has_active_subscriptions()
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();
        $this->assertCount(0, $subscribable->activeSubscriptions);

        $subscription = $this->createSubscription();
        $subscribable->oldSubscriptions()->save($subscription);
        $subscription->forceFill([
            'started_at' => now(),
            'ends_at' => now()->subDay(),
        ])->save();

        $this->assertCount(0, $subscribable->refresh()->activeSubscriptions);
        $subscription->forceFill([
            'started_at' => now(),
            'ends_at' => now()->addMonth(),
        ])->save();

        $this->assertCount(1, $subscribable->refresh()->activeSubscriptions);

        $subscription->forceFill([
            'started_at' => now()->addDay(),
            'ends_at' => null,
        ])->save();

        $this->assertCount(0, $subscribable->refresh()->activeSubscriptions);

        $subscription->forceFill([
            'started_at' => now(),
            'ends_at' => null,
        ])->save();
        $this->assertCount(1, $subscribable->refresh()->activeSubscriptions);
    }

    public function test_subscription_already_active(): void
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();
        $subscription = $this->createSubscription([
            'subscribable_id' => $subscribable->id,
        ]);
        $subscribable->oldSubscriptions()->save($subscription);

        $subscription->forceFill([
            'started_at' => now(),
            'ends_at' => null,
        ])->save();

        $newSubscription = $subscribable->subscribeToPlan('base');
        $this->assertCount(1, $subscribable->refresh()->activeSubscriptions);
        $this->assertCount(1, $subscribable->refresh()->oldSubscriptions);
        $this->assertSame($subscription->id, $newSubscription->id);
    }

    public function test_upgrade_subscription(): void
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();
        $subscription = $this->createSubscription([
            'subscribable_id' => $subscribable->id,
        ]);
        $subscription->plan = 'wl_base';
        $subscription->start();
        $subscribable->oldSubscriptions()->save($subscription);

        $newSubscription = $subscribable->subscribeToPlan('wl_extended');
        // on day of upgrade both subscriptions are active
        $this->assertCount(2, $subscribable->refresh()->activeSubscriptions);
        $this->travel(1)->days();
        $this->assertCount(1, $subscribable->refresh()->activeSubscriptions);

        $this->assertCount(2, $subscribable->refresh()->oldSubscriptions);
        $this->assertNotSame($subscription->id, $newSubscription->id);
        $this->assertEquals('wl_extended', $newSubscription->plan);
    }

    public function test_start_first_subscription(): void
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();

        $subscription = $subscribable->subscribeToPlan('wl_base');
        $this->assertSame($subscription->id, $subscribable->activeSubscriptions->first()->id);
        $this->assertSame(now()->startOfDay()->getTimestamp(), $subscription->started_at->getTimestamp());
        $this->assertSame('wl_base', $subscription->plan);
    }

    public function test_downgrade_subscription(): void
    {
        $subscribable = Pharmacy::factory()->create();
        $subscription = $this->createSubscription();
        $subscription->plan = 'wl_extended';
        $subscription->start();
        $subscribable->oldSubscriptions()->save($subscription);

        $newSubscription = $subscribable->subscribeToPlan('wl_base');
        $this->assertCount(1, $subscribable->refresh()->oldSubscriptions);
        $this->assertSame($subscription->id, $newSubscription->id);
        $this->assertSame('wl_extended', $newSubscription->plan);
        $this->assertSame('wl_base', $newSubscription->next_plan);
    }

    public function test_add_initial_order_for_no_orderables(): void
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();

        $subscribable->addInitialOrderForSubscriptionForOrderables(
            $this->createSubscription()
        );
        $this->assertCount(1, SubscriptionOrder::all());
    }

    public function test_add_initial_order_for_2_pharmacies(): void
    {
        [$u, $subscribable] = $this->createPharmacyUserWithPharmacy();
        $subscription = $this->createSubscription([
            'subscribable_id' => $subscribable->id,
        ]);
        $pharmacy1 = Pharmacy::factory()->create();
        $pharmacy2 = Pharmacy::factory()->create();
        $pharmacy1->users()->attach($subscribable, ['role_name' => PharmacyRoleEnum::OWNER]);
        $pharmacy2->users()->attach($subscribable, ['role_name' => PharmacyRoleEnum::OWNER]);

        $subscribable->addInitialOrderForSubscriptionForOrderables($subscription);
        $this->assertCount(1, SubscriptionOrder::all());
    }

    public function test_has_active_subscription(): void
    {
        $subscribable = Pharmacy::factory()->create();
        $this->assertFalse($subscribable->hasActiveSubscription());
        $subscription = $this->createSubscription();
        $subscribable->oldSubscriptions()->save($subscription);
        $this->assertFalse($subscribable->hasActiveSubscription());
    }

    public function test_is_subscribed_to_base(): void
    {
        $subscribable = $this->createPharmacy();
        BrochureCode::factory()->create(['user_id' => $subscribable->id]);
        UserPharmacyProfile::create(['user_id' => $subscribable->id]);
        $this->assertFalse($subscribable->isSubscribedTo('base'));
        $subscription = $this->createSubscription();
        $subscribable->oldSubscriptions()->save($subscription);
        $this->assertTrue($subscribable->refresh()->isSubscribedToOneOf(['base', 'extended']));
        $this->assertFalse($subscribable->refresh()->isSubscribedToOneOf(['extern_base', 'wl_extended']));
    }
}
