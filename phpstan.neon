includes:
    - vendor/larastan/larastan/extension.neon
    - phpstan-baseline.neon

parameters:
    tmpDir: storage/phpstan
    paths:
        - app/

    level: 9

    ignoreErrors:
        - '#Call to an undefined method Illuminate\\Support\\HigherOrder#'
        - '#PHPDoc tag @mixin contains unknown class#'
        - '#Access to an undefined property [a-zA-Z0-9\\_]+::\$pivot#' # larastan will hopefully fix this in a future version https://github.com/larastan/larastan/issues/1774
        - '#Call to an undefined method Laravel\\Nova\\Fields\\Date::dependsOn\(\)\.#'
        - identifier: missingType.generics

    reportUnmatchedIgnoredErrors: false
#
#    excludePaths:
#        - ./*/*/FileToBeExcluded.php
#
#    checkMissingIterableValueType: false
