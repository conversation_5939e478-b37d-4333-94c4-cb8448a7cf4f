<?php

namespace App\Policies;

use App\Staff;
use App\User;
use Illuminate\Database\Eloquent\Model;

trait NovaDefaultFunctions
{
    public function viewAny(User|Staff $user): bool
    {
        return true;
    }

    public function view(User|Staff $user, Model $model): bool
    {
        return true;
    }

    public function create(User|Staff $user): bool
    {
        return true;
    }

    public function update(User|Staff $user, Model $model): bool
    {
        return true;
    }

    public function delete(User|Staff $user, Model $model): bool
    {
        return true;
    }

    public function restore(User|Staff $user, Model $model): bool
    {
        return true;
    }

    public function forceDelete(User|Staff $user, Model $model): bool
    {
        return true;
    }
}
