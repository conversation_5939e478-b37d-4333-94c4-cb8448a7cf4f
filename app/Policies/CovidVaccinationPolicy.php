<?php

namespace App\Policies;

use App\Association;
use App\CovidVaccination;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CovidVaccinationPolicy
{
    use HandlesAuthorization;

    public function index(User $user, Pharmacy $pharmacy)
    {
        return $this->store($user, $pharmacy);
    }

    public function store(User $user, Pharmacy $pharmacy)
    {
        return

                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::VACCINATE_COVID);
    }

    public function update(User $user, CovidVaccination $covidVaccination)
    {
        return $this->store($user, $covidVaccination->vaccination->pharmacy);
    }

    private function getAssociation(Pharmacy $pharmacy)
    {
        if (! ($pharmacy->owner() && $pharmacy->owner()->pharmacyProfile && $pharmacy->owner()->pharmacyProfile->association)) {
            return false;
        }

        /** @var Association $association */
        return $pharmacy->owner()->pharmacyProfile->association;
    }

    public function viewAny(User $user, Pharmacy $pharmacy)
    {
        return

                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::VACCINATE_COVID);
    }
}
