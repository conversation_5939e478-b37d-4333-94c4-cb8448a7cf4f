<?php

namespace App\Nova;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Nova\Actions\Subscription\ConfirmCancellationNovaAction;
use App\Nova\Actions\Subscription\RevokeCancellationNovaAction;
use App\SubscriptionCancellation as SubscriptionCancellationModel;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Resource;

class SubscriptionCancellation extends Resource
{
    public static $model = SubscriptionCancellationModel::class;

    public static $title = 'id';

    public static $search = [
        'id', 'pharmacy.name',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label(): string
    {
        return 'Kündigungen';
    }

    public static function singularLabel(): string
    {
        return 'Kündigung';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Apotheke', 'pharmacy', Pharmacy::class)
                ->searchable()
                ->sortable(),

            BelongsTo::make('Benutzer', 'user', User::class)
                ->searchable()
                ->sortable(),

            Badge::make('Status', 'status')
                ->map([
                    SubscriptionCancellationStatusEnum::PENDING->value => 'warning',
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => 'info',
                    SubscriptionCancellationStatusEnum::REVOKED->value => 'danger',
                    SubscriptionCancellationStatusEnum::EXECUTED->value => 'success',
                ])
                ->labels([
                    SubscriptionCancellationStatusEnum::PENDING->value => 'Ausstehend',
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => 'Bestätigt',
                    SubscriptionCancellationStatusEnum::REVOKED->value => 'Widerrufen',
                    SubscriptionCancellationStatusEnum::EXECUTED->value => 'Ausgeführt',
                ])
                ->sortable(),

            DateTime::make('Beantragt am', 'requested_at')
                ->format('DD.MM.YYYY HH:mm')
                ->sortable(),

            DateTime::make('Kündigungsdatum', 'effective_date')
                ->format('DD.MM.YYYY')
                ->sortable(),

            DateTime::make('Bestätigt am', 'confirmed_at')
                ->format('DD.MM.YYYY HH:mm')
                ->hideFromIndex()
                ->nullable(),

            DateTime::make('Widerrufen am', 'revoked_at')
                ->format('DD.MM.YYYY HH:mm')
                ->hideFromIndex()
                ->nullable(),

            DateTime::make('Ausgeführt am', 'executed_at')
                ->format('DD.MM.YYYY HH:mm')
                ->hideFromIndex()
                ->nullable(),

            Textarea::make('Grund', 'reason')
                ->hideFromIndex()
                ->nullable(),

            Text::make('Metadaten', function () {
                if (!$this->metadata) {
                    return '-';
                }
                return json_encode($this->metadata, JSON_PRETTY_PRINT);
            })
                ->asHtml()
                ->hideFromIndex()
                ->onlyOnDetail(),

            DateTime::make('Erstellt am', 'created_at')
                ->format('DD.MM.YYYY HH:mm')
                ->hideFromIndex()
                ->sortable(),

            DateTime::make('Aktualisiert am', 'updated_at')
                ->format('DD.MM.YYYY HH:mm')
                ->hideFromIndex()
                ->sortable(),
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            ConfirmCancellationNovaAction::make()
                ->canSee(function ($request) {
                    return $this->resource->status === SubscriptionCancellationStatusEnum::PENDING;
                })
                ->canRun(function ($request, $model) {
                    return $model->status === SubscriptionCancellationStatusEnum::PENDING;
                }),

            RevokeCancellationNovaAction::make()
                ->canSee(function ($request) {
                    return in_array($this->resource->status, [
                        SubscriptionCancellationStatusEnum::PENDING,
                        SubscriptionCancellationStatusEnum::CONFIRMED
                    ]) && $this->resource->canBeRevoked();
                })
                ->canRun(function ($request, $model) {
                    return $model->canBeRevoked();
                }),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\SubscriptionCancellationStatusFilter,
        ];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->with(['pharmacy', 'user']);
    }
}
