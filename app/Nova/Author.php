<?php

namespace App\Nova;

use App\Enums\SalutationEnum;
use App\Rules\GedisaMail;
use <PERSON><PERSON>s\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Author extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Author::class;

    //    /**
    //     * The single value that should be used to represent the resource when being displayed.
    //     *
    //     * @var string
    //     */
    //    public static $title = 'last_name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'first_name',
        'last_name',
        'website',
        'description',
    ];

    public static $group = 'News/Blog';

    public static function label()
    {
        return 'Autoren';
    }

    /**
     * Get the value that should be displayed to represent the resource. [Overwrite public static $title]
     *
     * @return string
     */
    public function title()
    {
        return $this->fullName;
    }

    public static function trafficCop(Request $request)
    {
        return false;
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        $resource = $this->resource;

        return [
            ID::make()->sortable(),

            Select::make('Salutation')
                ->options(SalutationEnum::getForDropdown()->map(function ($val) {
                    return trans('validation.attributes.'.$val);
                }))
                ->required()
                ->displayUsingLabels()
                ->onlyOnForms(),

            Text::make('Title')
                ->sortable()
                ->rules([
                    'max:254',
                ])
                ->onlyOnForms(),

            Text::make('Name', function () {
                return $this->fullname;
            }),

            Text::make('First_name')
                ->sortable()
                ->rules([
                    'required',
                    'max:254',
                    'string',
                ])
                ->onlyOnForms(),

            Text::make('Last_name')
                ->sortable()
                ->rules(['required', 'max:254', 'string'])
                ->onlyOnForms(),

            Text::make('Slug')
                ->rules('required', 'max:254')
                ->creationRules('unique:authors,slug')
                ->updateRules('unique:authors,slug,{{resourceId}}')
                ->onlyOnForms(),

            Text::make('Email')
                ->sortable()
                ->rules('required', app(GedisaMail::class))
                ->creationRules('unique:authors,email')
                ->updateRules('unique:authors,email,{{resourceId}}'),

            Text::make('Website')
                ->sortable()
                ->rules([
                    'max:254',
                ])
                ->hideFromIndex(),

            Images::make('image', 'image')
                ->setAllowedFileTypes(['image/jpeg', 'image/png']),

            Textarea::make('Description')
                ->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
