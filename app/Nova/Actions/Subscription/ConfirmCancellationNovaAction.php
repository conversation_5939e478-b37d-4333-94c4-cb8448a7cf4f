<?php

namespace App\Nova\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Mail\SubscriptionCancellationRequestedMail;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class ConfirmCancellationNovaAction extends Action
{
    public $name = 'Kündigung bestätigen';

    public $confirmText = 'Soll die Kündigung bestätigt werden?';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        if ($models->count() > 1) {
            return Action::danger('Bitte nur eine Kündigung auswählen.');
        }

        $cancellation = $models->first();
        assert($cancellation instanceof SubscriptionCancellation);

        if ($cancellation->status !== SubscriptionCancellationStatusEnum::PENDING) {
            return Action::danger('Nur ausstehende Kündigungen können bestätigt werden.');
        }

        $oldData = [
            'status' => $cancellation->status->value,
        ];

        // Kündigung bestätigen
        $cancellation->update([
            'status' => SubscriptionCancellationStatusEnum::CONFIRMED,
            'confirmed_at' => now(),
        ]);

        // Change History erstellen
        SubscriptionChangeHistory::log(
            pharmacy: $cancellation->pharmacy,
            action: 'cancellation_confirmed',
            user: auth()->user(),
            oldData: $oldData,
            newData: [
                'status' => SubscriptionCancellationStatusEnum::CONFIRMED->value,
                'confirmed_at' => now()->toDateTimeString(),
            ],
            notes: $fields->get('notes') ?: 'Kündigung über Nova bestätigt',
            source: 'nova'
        );

        // E-Mail-Benachrichtigung senden
        $this->sendConfirmationNotification($cancellation);

        return Action::message('Die Kündigung wurde erfolgreich bestätigt.');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Interne Notiz', 'notes')
                ->rows(3)
                ->help('Optional: Grund für die Bestätigung oder zusätzliche Informationen.')
                ->nullable(),
        ];
    }

    private function sendConfirmationNotification(SubscriptionCancellation $cancellation): void
    {
        $pharmacy = $cancellation->pharmacy;
        $emails = collect();

        // Rechnungsadresse
        if ($pharmacy->billingAddress?->email) {
            $emails->push($pharmacy->billingAddress->email);
        }

        // Inhaber und Subinhaber
        $pharmacy->ownersAndSubowners()
            ->pluck('email')
            ->each(fn ($email) => $emails->push($email));

        $emails->unique()->each(function ($email) use ($pharmacy, $cancellation) {
            Mail::to($email)->send(new SubscriptionCancellationRequestedMail($pharmacy, $cancellation));
        });
    }
}
