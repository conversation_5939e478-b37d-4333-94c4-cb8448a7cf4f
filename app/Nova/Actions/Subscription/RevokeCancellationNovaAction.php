<?php

namespace App\Nova\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class RevokeCancellationNovaAction extends Action
{
    public $name = 'Kündigung widerrufen';

    public $confirmText = 'Soll die Kündigung widerrufen werden?';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        if ($models->count() > 1) {
            return Action::danger('Bitte nur eine Kündigung auswählen.');
        }

        $cancellation = $models->first();
        assert($cancellation instanceof SubscriptionCancellation);

        if (!$cancellation->canBeRevoked()) {
            return Action::danger('Diese Kündigung kann nicht mehr widerrufen werden.');
        }

        $oldData = [
            'status' => $cancellation->status->value,
            'effective_date' => $cancellation->effective_date->toDateString(),
        ];

        // Kündigung widerrufen
        $cancellation->update([
            'status' => SubscriptionCancellationStatusEnum::REVOKED,
            'revoked_at' => now(),
        ]);

        // Change History erstellen
        SubscriptionChangeHistory::log(
            pharmacy: $cancellation->pharmacy,
            action: 'cancellation_revoked',
            user: auth()->user(),
            oldData: $oldData,
            newData: [
                'status' => SubscriptionCancellationStatusEnum::REVOKED->value,
                'revoked_at' => now()->toDateTimeString(),
            ],
            notes: $fields->get('notes') ?: 'Kündigung über Nova widerrufen',
            source: 'nova'
        );

        return Action::message('Die Kündigung wurde erfolgreich widerrufen.');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Grund für Widerruf', 'notes')
                ->rows(3)
                ->help('Bitte geben Sie den Grund für den Widerruf an.')
                ->required(),
        ];
    }
}
