<?php

namespace App\Nova\Filters;

use App\Enums\KimAddressStatus as KimAddressStatusEnum;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class KimAddressStatus extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @return array
     */
    public function options(Request $request)
    {
        return KimAddressStatusEnum::toAssociativeArray();
    }
}
