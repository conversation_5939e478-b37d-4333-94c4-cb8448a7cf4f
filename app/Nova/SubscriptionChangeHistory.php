<?php

namespace App\Nova;

use App\SubscriptionChangeHistory as SubscriptionChangeHistoryModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Resource;

class SubscriptionChangeHistory extends Resource
{
    public static $model = SubscriptionChangeHistoryModel::class;

    public static $title = 'id';

    public static $search = [
        'id', 'pharmacy.name', 'action',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label(): string
    {
        return 'Änderungshistorie';
    }

    public static function singularLabel(): string
    {
        return 'Änderung';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Apotheke', 'pharmacy', Pharmacy::class)
                ->searchable()
                ->sortable(),

            BelongsTo::make('Benutzer', 'user', User::class)
                ->searchable()
                ->sortable()
                ->nullable(),

            Text::make('Aktion', 'formatted_action')
                ->sortable(),

            Text::make('Quelle', 'source')
                ->sortable(),

            Text::make('Akteur', 'actor_name')
                ->hideFromIndex(),

            Text::make('Alte Daten', function () {
                if (!$this->old_data) {
                    return '-';
                }
                return '<pre>' . json_encode($this->old_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            })
                ->asHtml()
                ->hideFromIndex()
                ->onlyOnDetail(),

            Text::make('Neue Daten', function () {
                if (!$this->new_data) {
                    return '-';
                }
                return '<pre>' . json_encode($this->new_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            })
                ->asHtml()
                ->hideFromIndex()
                ->onlyOnDetail(),

            Textarea::make('Notizen', 'notes')
                ->hideFromIndex()
                ->nullable(),

            DateTime::make('Erstellt am', 'created_at')
                ->format('DD.MM.YYYY HH:mm')
                ->sortable(),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \App\Nova\Filters\SubscriptionChangeHistoryActionFilter,
            new \App\Nova\Filters\SubscriptionChangeHistorySourceFilter,
        ];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->with(['pharmacy', 'user'])->latest();
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return false; // Change History sollte nur programmatisch erstellt werden
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return false; // Change History sollte nicht bearbeitet werden
    }

    public function authorizedToDelete(Request $request): bool
    {
        return false; // Change History sollte nicht gelöscht werden
    }
}
