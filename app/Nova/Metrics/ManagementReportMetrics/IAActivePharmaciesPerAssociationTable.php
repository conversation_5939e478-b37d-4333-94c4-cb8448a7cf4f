<?php

namespace App\Nova\Metrics\ManagementReportMetrics;

use App\Integrations\IaIntegration;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\PartitionResult;

class IAActivePharmaciesPerAssociationTable extends ManagementReportMetric
{
    public function name(): string
    {
        return 'Apotheken mit aktiver iA Integration pro Verband';
    }

    public function calculate(NovaRequest $request): PartitionResult
    {
        $results = $this->getPharmacyGroupQuery()
            ->whereHas('integrations', function ($q) {
                $q->where('integration_type', IaIntegration::class);
            })
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->id => $item->Anzahl];
            });

        return $this->formatResult($results->toArray());
    }
}
