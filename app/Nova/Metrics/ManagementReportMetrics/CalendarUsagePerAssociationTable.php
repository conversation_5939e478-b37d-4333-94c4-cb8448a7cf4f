<?php

namespace App\Nova\Metrics\ManagementReportMetrics;

use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\PartitionResult;

class CalendarUsagePerAssociationTable extends ManagementReportMetric
{
    public function name(): string
    {
        return 'Kalender nutzende Apotheken pro Verband';
    }

    public function calculate(NovaRequest $request): PartitionResult
    {
        $pharmacies = $this->getPharmacyGroupQuery()
            ->where('uses_calendar', true)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->id => $item->Anzahl];
            });

        return $this->formatResult($pharmacies->toArray());
    }
}
