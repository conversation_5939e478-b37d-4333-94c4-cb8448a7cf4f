<?php

namespace App\Nova\Metrics\ManagementReportMetrics;

use App\Enums\CardLink\CardLinkOrderStatusEnum;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\PartitionResult;

class CardLinkPharmacyPerAssociationTable extends ManagementReportMetric
{
    public function name(): string
    {
        return 'Apotheken mit aktivem CardLink pro Verband';
    }

    public function calculate(NovaRequest $request): PartitionResult
    {
        $results = $this->getPharmacyGroupQuery()
            ->whereHas('cardLinkOrder', function ($q) {
                $q->where('status', CardLinkOrderStatusEnum::Activated);
            })
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->id => $item->Anzahl];
            });

        return $this->formatResult($results->toArray());
    }
}
