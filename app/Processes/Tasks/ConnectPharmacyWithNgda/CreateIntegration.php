<?php

namespace App\Processes\Tasks\ConnectPharmacyWithNgda;

use App\Http\Integrations\NGDA\NGDAConnector;
use App\Integrations\NGDAIntegration;
use App\Processes\Payloads\ConnectPharmacyWithNgdaPayload;
use Closure;
use RuntimeException;

class CreateIntegration
{
    public function __invoke(ConnectPharmacyWithNgdaPayload $payload, Closure $next): ConnectPharmacyWithNgdaPayload
    {
        assert($payload->integration instanceof NGDAIntegration);

        $token = NGDAConnector::decodeToken($payload->integration->accessToken);
        assert(is_string($token));

        $token = (object) json_decode($token);

        if ($payload->integration->id !== $token->n_id) {
            throw new RuntimeException(sprintf('N-ID <%s> der Integration weicht bereits vor der Aktualisierung von der N-ID <%s> im Token ab.', $payload->integration->id, $token->n_id));
        }

        $pharmacy = $payload->pharmacy;
        $pharmacy->setIntegration($payload->integration);
        $pharmacy->n_id = $payload->id;
        $pharmacy->save();

        return $next($payload);
    }
}
