<?php

namespace App\Processes\Tasks\ProcessAssociationMembershipChange;

use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;

class FinishChange
{
    public function __invoke(ProcessAssociationMembershipChangePayload $payload, \Closure $next): ProcessAssociationMembershipChangePayload|bool
    {
        $payload->associationMembershipChange->touch('change_done_at');

        return true;
    }
}
