<?php

namespace App\Processes;

use App\Processes\Tasks\OrderCardLink\ActivateApoGuide;
use App\Processes\Tasks\OrderCardLink\CreateCardLinkOrder;
use App\Processes\Tasks\OrderCardLink\CreateStripeCustomer;
use App\Processes\Tasks\OrderCardLink\CreateStripeOrder;
use App\Processes\Tasks\OrderCardLink\OnFailure\DeactivateApoGuide;
use App\Processes\Tasks\OrderCardLink\OnFailure\DeleteCardLinkSettings;
use App\Processes\Tasks\OrderCardLink\OnFailure\DeleteStripeOrder;
use App\Processes\Tasks\OrderCardLink\SendActivationConfirmation;
use App\Processes\Tasks\OrderCardLink\SendCardLinkOrderToCardLinkService;
use App\Processes\Tasks\OrderCardLink\SendOrderConfirmation;

class OrderCardLink extends Process
{
    public function tasks(): array
    {
        return [
            CreateCardLinkOrder::class,
            SendCardLinkOrderToCardLinkService::class,
            ActivateApoGuide::class,
            CreateStripeCustomer::class,
        ];
    }

    public function onSuccess(): array
    {
        return [
            CreateStripeOrder::class,
            SendOrderConfirmation::class,
            SendActivationConfirmation::class,
        ];
    }

    public function onFailure(): array
    {
        return [
            DeactivateApoGuide::class,
            DeleteCardLinkSettings::class,
            DeleteStripeOrder::class,
        ];
    }
}
