<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;

class GedisaMail implements ValidationRule
{
    /**
     * @var array<string>
     */
    protected array $allowed = [
        'example.com',
    ];

    /**
     * @var array<string>
     */
    protected array $only = [];

    protected ?bool $shouldUseDns = null;

    public function allow(string $domain): self
    {
        $domain = str($domain);

        if ($domain->contains('@')) {
            $domain = $domain->afterLast('@');
        }

        $this->allowed[] = $domain->value();

        return $this;
    }

    public function only(string $domain): self
    {
        $domain = str($domain);

        if ($domain->contains('@')) {
            $domain = $domain->afterLast('@');
        }

        $this->only[] = $domain->value();

        return $this;
    }

    public function allowApomail(): self
    {
        return $this->allow('apomail.de');
    }

    public function onlyApomail(): self
    {
        return $this->only('apomail.de');
    }

    public function dns(bool $shouldUseDns = true): self
    {
        $this->shouldUseDns = $shouldUseDns;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        assert(is_string($value));

        if (str($value)->contains(' ')) {
            $fail('Die angegebene E-Mail-Adresse enthält ungültige Leerzeichen.');
        }

        $domain = str($value)->afterLast('@');

        $rules = [
            $attribute => [
                match ($this->shouldUseDns($domain)) {
                    true => 'email:rfc,dns',
                    default => 'email:rfc',
                },
                'max:255',
            ],
        ];

        $validator = Validator::make(
            Arr::undot([$attribute => $value]),
            $rules
        );

        if (! $validator->passes()) {
            $fail('Diese E-Mail-Adresse ist ungültig.');
        }

        if ($this->only) {
            if (in_array($domain->value(), $this->only, true) === false) {
                $fail('Diese Domain ist nicht erlaubt.');
            }
        }
    }

    public function shouldUseDns(string $domain): bool
    {
        if ($this->shouldUseDns) {
            return true;
        }

        if ($this->allowed && in_array($domain, $this->allowed, true)) {
            return false;
        }

        return config('app.email_validation_environment') === 'production';
    }
}
