<?php

namespace App\Jobs;

use App\Vaccination;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteUnfinishedVaccinations implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Vaccination::where('updated_at', '<=', now()->subDay())
            ->where('status', '0')
            ->lazyById()
            ->each(function ($vaccination) {
                $vac = Vaccination::findOrFail($vaccination->id);
                $vac->delete();
            });
    }
}
