<?php

namespace App\Mail;

use App\Pharmacy;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AllowSendingKimAddressToNNF extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Pharmacy $pharmacy
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Jetzt TI-Pauschale sichern & KIM Adresse an NNF melden',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.kim-address.allow-sending-to-nnf',
        );
    }
}
