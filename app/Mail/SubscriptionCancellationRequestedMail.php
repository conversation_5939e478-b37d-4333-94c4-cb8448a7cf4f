<?php

namespace App\Mail;

use App\Pharmacy;
use App\SubscriptionCancellation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SubscriptionCancellationRequestedMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Pharmacy $pharmacy,
        public SubscriptionCancellation $cancellation
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Kündigungsbestätigung - Ihre Basismitgliedschaft',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.subscription.cancellation-requested',
        );
    }
}
