<?php

namespace App\Http\Controllers\Api;

use App\CardLinkOrder;
use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\CardLinkPacketMPlusStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\CardLinkPacketMStripeProduct;
use App\Domains\Subscription\Domain\Jobs\AddOneTimePurchaseToSubscriptionJob;
use App\GedisaId;
use App\Http\Controllers\Controller;
use App\Http\Requests\CardLink\NotificationRequest;
use App\Notifications\CardLinkTransactionLimitAlertNotification;
use App\Notifications\CardLinkTransactionLimitWarningNotification;
use App\Pharmacy;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CardLinkController extends Controller
{
    public function limitNotification(Request $request): JsonResponse
    {
        $pharmacy = Pharmacy::query()->whereRelation('gedisaId', 'id', $request->gedisa_id)->first();

        if (! ($pharmacy instanceof Pharmacy)) {
            return response()->json(['error' => 'No pharmacy found for Gedisa ID `'.$request->gedisa_id.'`'], 400);
        }
        if (! is_float($request->usage_percentage) && ! is_int($request->usage_percentage)) {
            return response()->json(['error' => '`usage_percentage` must be of type integer or float'], 400);
        }
        if (! is_int($request->current_usage)) {
            return response()->json(['error' => '`current_usage` must be of type integer'], 400);
        }
        if (! is_null($request->limit) && ! is_int($request->limit)) {
            return response()->json(['error' => '`limit` must be of type integer or null'], 400);
        }
        if (! ($pharmacy->cardLinkOrder instanceof CardLinkOrder)) {
            return response()->json(['error' => 'No card link order found for pharmacy `'.$pharmacy->name.'`'], 400);
        }

        $roundedUsagePercentage = round($request->usage_percentage);
        if ($request->usage_percentage < 0 || $request->usage_percentage > 100) {
            return response()->json(['error' => '`usage_percentage` must be greater than 0 and lower than 100'], 400);
        }

        if ((int) $roundedUsagePercentage === 80) {
            /** @phpstan-ignore-next-line callable == Closure */
            $pharmacy->ownersAndSubowners()->each(fn (User $user) => $user->notify(new CardLinkTransactionLimitWarningNotification(
                $pharmacy,
                $request->usage_percentage
            )));
        }
        if ((int) $roundedUsagePercentage === 100) {
            /** @phpstan-ignore-next-line callable == Closure */
            $pharmacy->ownersAndSubowners()->each(fn (User $user) => $user->notify(new CardLinkTransactionLimitAlertNotification(
                $pharmacy,
                $request->usage_percentage
            )));
        }

        $pharmacy->searchable();

        return response()->json([], 204);
    }

    public function notification(NotificationRequest $request): JsonResponse
    {
        $pharmacy = GedisaId::findOrFail($request->string('gedisa_id'))->pharmacy;

        if (! ($pharmacy instanceof Pharmacy)) {
            return response()->json(['error' => 'No pharmacy found for Gedisa ID `'.$request->gedisa_id.'`'], 400);
        }

        AddOneTimePurchaseToSubscriptionJob::dispatch(
            $pharmacy,
            $request->integer('new_limit') === 250 ? CardLinkPacketMStripeProduct::make() : CardLinkPacketMPlusStripeProduct::make(),
            now('Europe/Berlin')
        );

        return response()->json([
            'message' => 'The new limit has been set successfully.',
        ]);
    }
}
