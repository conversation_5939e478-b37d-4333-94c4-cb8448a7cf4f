<?php

namespace App\Http\Controllers;

use App\Association;
use App\Enums\PharmacyRoleEnum;
use App\Http\Requests\AssociationRequest;
use App\Pharmacy;
use App\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\View\View;

class AssociationController extends Controller
{
    /**
     * @param  Association  $association
     *
     * @throws AuthorizationException
     */
    public function overview(): View
    {
        $association = user()->associations->first();

        $this->authorize('view', $association);

        SEOMeta::setTitle($association->name);

        return view('association.overview', [
            'association' => $association,
            'pharmaciesCount' => $this->getPharmacies($association)->count(),
            'ownersCount' => $this->getOwnersWithBrochureCodes($association)->count(),
            'allUsersCount' => $this->getUsersInAssociationPharmacies($association)->count(),
            'pharmaciesWithRapidTestCount' => $this->getPharmaciesWithRapidTest($association)->count(),
        ]);
    }

    public function edit()
    {
        $association = user()->associations->first();

        $this->authorize('update', $association);

        SEOMeta::setTitle($association->name);

        return view('association.edit', [
            'association' => $association,
        ]);
    }

    public function update(AssociationRequest $request)
    {
        $association = user()->associations->first();

        $this->authorize('update', $association);

        $validated = $request->validated();

        $association->update($validated);

        notify(__('notifications.association.updated'));

        return redirect()->route('associations.overview');
    }

    private function getPharmacies(Association $association)
    {
        return Pharmacy::query()
            ->whereHas('users', function ($query) use ($association) {
                return $query
                    ->where('role_name', PharmacyRoleEnum::OWNER)
                    ->forAssociation($association)
                    ->whereHas('brochureCode');
            });
    }

    private function getOwnersWithBrochureCodes(Association $association)
    {
        return User::query()
            ->forAssociation($association)
            ->whereHas('brochureCode');
    }

    private function getUsersInAssociationPharmacies(Association $association)
    {
        return User::query()
            ->whereHas('pharmacies', function ($query) use ($association) {
                return $query->whereHas('users', function ($query) use ($association) {
                    return $query
                        ->where('role_name', PharmacyRoleEnum::OWNER)
                        ->forAssociation($association)
                        ->whereHas('brochureCode');
                });
            })
            ->orWhereHas('pharmacyProfile', function ($query) use ($association) {
                return $query->where('association_id', $association->id);
            });
    }

    private function getPharmaciesWithRapidTest(Association $association)
    {
        return Pharmacy::query()
            ->whereHas('users', function ($query) use ($association) {
                return $query
                    ->where('role_name', PharmacyRoleEnum::OWNER)
                    ->forAssociation($association)
                    ->whereHas('brochureCode');
            })
            ->where('corona_rapid_test', true)
            ->where('show_in_apoguide', true);
    }
}
