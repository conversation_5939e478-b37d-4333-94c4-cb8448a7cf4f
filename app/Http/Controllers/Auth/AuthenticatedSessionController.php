<?php

namespace App\Http\Controllers\Auth;

use App\Helper\OIDCHelper;
use App\Http\Controllers\Controller;
use App\Http\Integrations\NGDA\NGDAConnector;
use App\Http\Requests\LoginRequest;
use App\Providers\RouteServiceProvider;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        SEOMeta::setTitle(Lang::get('Login'));

        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        return redirect()->intended(RouteServiceProvider::HOME);
    }

    /**
     * Destroy an authenticated session.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, OIDCHelper $OIDCHelper)
    {
        if ($pharmacy = currentPharmacy()) {
            app(NGDAConnector::class, [$pharmacy])->logoutFromSession(true);
        }

        $OIDCHelper->logUserOutAtIdp();

        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
