<?php

namespace App\Http\Controllers;

use App\Enums\InhalationTechnique\InhalationTechniqueStepEnum;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use App\InhalationTechniquePatient;
use App\PharmaceuticalService;
use App\Pharmacy;
use Illuminate\Support\Facades\DB;

class PharmacyInhalationTechniqueController extends Controller
{
    public function index(Pharmacy $pharmacy)
    {
        $this->authorize('viewAny', [InhalationTechniquePatient::class, $pharmacy]);

        return view('pharmacy.inhalationTechnique.index', [
            'pharmacy' => $pharmacy,
            'inhalation_techniques' => $pharmacy->pharmaceuticalServices()->where('type', PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE)->orderBy('id', 'DESC')->paginate(8),
        ]);
    }

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function start(Pharmacy $pharmacy)
    {
        $this->authorize('store', [InhalationTechniquePatient::class, $pharmacy]);

        return view('pharmacy.inhalationTechnique.start', ['pharmacy' => $pharmacy]);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function store(Pharmacy $pharmacy)
    {
        $this->authorize('store', [InhalationTechniquePatient::class, $pharmacy]);

        request()->validate([
            'accepted' => 'accepted',
        ]);

        DB::beginTransaction();

        try {
            /** @var PharmaceuticalService $pharmaceuticalService */
            $pharmaceuticalService = $pharmacy->pharmaceuticalServices()->create([
                'association_id' => $pharmacy->owner()->pharmacyProfile->association_id,
                'user_id' => user()->id,
                'date' => now()->startOfDay(),
                'type' => PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE,
            ]);

            $pharmaceuticalService->pharmaceuticalServicePatient()->create();
            $pharmaceuticalService->inhalationTechniquePatient()->create();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return response()->redirectToRoute('pharmacies.pharmaceutical-services.inhalation-techniques.personal-data', [$pharmacy, $pharmaceuticalService]);
    }

    public function edit(Pharmacy $pharmacy, PharmaceuticalService $pharmaceuticalService)
    {
        abort_unless($pharmaceuticalService->type === PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE, 403);
        if (! $pharmaceuticalService->inhalationTechniquePatient) {
            $this->authorize('view-any', [InhalationTechniquePatient::class, $pharmacy]);
        } else {
            $this->authorize('update', $pharmaceuticalService->inhalationTechniquePatient);
        }

        return \redirect()->route(InhalationTechniqueStepEnum::getRedirectRoute($pharmaceuticalService), [$pharmacy, $pharmaceuticalService]);
    }
}
