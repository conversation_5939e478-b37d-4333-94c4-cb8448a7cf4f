<?php

namespace App\Http\Integrations\CardLinkService\Requests;

use App\Pharmacy;
use Saloon\Enums\Method;
use Saloon\Http\Request;

class GetChannelsRequest extends Request
{
    protected Method $method = Method::GET;

    public function __construct(
        protected Pharmacy $pharmacy,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf('/api/v1/pharmacies/%s/channels', $this->pharmacy->gedisaId?->id);
    }
}
