<?php

namespace App\Http\Middleware;

use App\Domains\Subscription\Application\FeatureAccess\DataAccessPeriodFeatureAccess;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\Settings\PharmacySettingTypes;
use App\Features\StripeFeature;
use App\Livewire\Terms\TermsDeadlineWarning;
use App\Settings\AppSettings;
use App\Settings\TermsOfServiceSettings;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Laravel\Pennant\Feature;

class EnsureTermsOfUseAreAccepted
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     *
     * @uses SetAllTermsOfServiceToNotAccepted::handle()
     */
    public function handle(Request $request, Closure $next)
    {
        if (! $user = user()) {
            return $next($request);
        }
        if (! $currentPharmacy = currentPharmacy()) {
            return $next($request);
        }

        // Do not remove this, as it could cause race conditions during testing
        $currentPharmacy->refresh();

        if (Carbon::now() < app(AppSettings::class)->terms_of_use_deadline) {
            $tosBeforeDeadline = $currentPharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->latest()
                ->first();
            $avvBeforeDeadline = $currentPharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->latest()
                ->first();
            $tosAndAvvWereAlreadyAcceptedWhenDeadlineStarts = $tosBeforeDeadline?->value === PharmacySettingTypes::TERMS_OF_USE_ACCEPTED
                                                              && $avvBeforeDeadline?->value === PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED;

            if ($tosAndAvvWereAlreadyAcceptedWhenDeadlineStarts) {
                return $next($request);
            }
        }

        if ($user->isAssociationUser()) {
            return $next($request);
        }

        $userIsOwnerOrSubOwner = $user->isOwnerOrSubOwner();

        if (Feature::active(StripeFeature::class)) {
            if ($userIsOwnerOrSubOwner && ! $currentPharmacy->isSubscribedToProduct(BaseStripeProduct::class) && ! DataAccessPeriodFeatureAccess::check($currentPharmacy)->canUse()) {
                return to_route('pharmacies.subscription', $currentPharmacy);
            }

            if ($userIsOwnerOrSubOwner && ! $currentPharmacy->hasAcceptedTermsAfter(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since)) {
                return to_route('pharmacies.termsOfUse', $currentPharmacy);
            }

            if (! $user->isOwnerOrSubOwner()) {
                if (! $currentPharmacy->hasAcceptedTerms()) {
                    return to_route('pharmacies.termsOfUseDeadline', $currentPharmacy);
                }
                if (session()->has(TermsDeadlineWarning::$key) === false && $currentPharmacy->hasAcceptedTermsBefore(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since)) {
                    return to_route('terms-deadline-warning');
                }
            }
        } else {
            if (! $currentPharmacy->hasAcceptedTerms()) {
                return to_route('pharmacies.termsOfUse', $currentPharmacy);
            }
        }

        return $next($request);
    }
}
