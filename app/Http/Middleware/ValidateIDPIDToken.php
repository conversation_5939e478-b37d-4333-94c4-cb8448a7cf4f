<?php

namespace App\Http\Middleware;

use App\Helper\OIDCHelper;
use Closure;
use Illuminate\Http\Request;

class ValidateIDPIDToken
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if ($valid = app(OIDCHelper::class)->validateIdToken($token) !== true) {
            return $valid;
        }

        return $next($request);
    }
}
