<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionCancellation
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $pharmacy = currentPharmacy();
        
        if (!$pharmacy) {
            return $next($request);
        }

        // Prüfe ob Features aufgrund einer Kündigung gesperrt sind
        if ($pharmacy->areFeaturesBlockedDueToCancellation()) {
            // Definiere gesperrte Routen
            $blockedRoutes = [
                'pharmacies.kim.*',
                'pharmacies.cardlink.*',
                'pharmacies.integrations.*',
                'pharmacies.subscription.order.*',
            ];

            $currentRoute = $request->route()->getName();
            
            foreach ($blockedRoutes as $pattern) {
                if (fnmatch($pattern, $currentRoute)) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'message' => 'Aufgrund einer ausstehenden Kündigung können keine neuen Features aktiviert werden.',
                            'cancellation_info' => [
                                'can_revoke' => true,
                                'revoke_url' => route('pharmacies.subscription', $pharmacy),
                            ]
                        ], 403);
                    }

                    return redirect()
                        ->route('pharmacies.subscription', $pharmacy)
                        ->with('error', 'Aufgrund einer ausstehenden Kündigung können keine neuen Features aktiviert werden. Sie können die Kündigung widerrufen, um wieder Zugang zu erhalten.');
                }
            }
        }

        return $next($request);
    }
}
