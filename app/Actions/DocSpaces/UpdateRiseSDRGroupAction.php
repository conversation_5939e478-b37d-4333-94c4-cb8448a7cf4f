<?php

namespace App\Actions\DocSpaces;

use App\Enums\Sdr\NameLengthEnum;
use App\Helper\RiseSDRApi;
use App\Pharmacy;
use Illuminate\Http\Client\RequestException;

class UpdateRiseSDRGroupAction extends AbstractRiseSDRAction
{
    protected string $name;

    protected string $sdrGroupId;

    protected Pharmacy $pharmacy;

    protected string $uniqueName;

    public function __construct(Pharmacy $pharmacy)
    {
        $this->pharmacy = $pharmacy;
    }

    public function execute(): self
    {
        $this->uniqueName = RiseSDRApi::formatUniqueName($this->pharmacy, $this->name, NameLengthEnum::GROUP);
        try {
            $response = app(RiseSDRApi::class)->updateGroup($this->sdrGroupId, $this->uniqueName);
        } catch (RequestException $e) {
            $this->handleErrors($e);
        }

        $this->sdrGroupId = $response->json('id');

        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setId(string $id): self
    {
        $this->sdrGroupId = $id;

        return $this;
    }

    public function getUniqueName(): string
    {
        return $this->uniqueName;
    }

    public function getErrorMessageFieldName(?string $sdrFieldName = null): string
    {
        return match ($sdrFieldName) {
            null => 'name',
            'name' => 'name',
            default => $sdrFieldName,
        };
    }
}
