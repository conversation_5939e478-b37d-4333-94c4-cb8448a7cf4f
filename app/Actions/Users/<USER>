<?php

namespace App\Actions\Users;

use App\User;

class DeletePharmacyEmployee
{
    public function execute(User $user)
    {
        // currently all connected entities for not owner pharmacy users are correctly cascaded by the database

        // Reset user login in order to make the email address and username available for registration
        $user->update([
            'email' => null,
            'username' => null,
        ]);
    }
}
