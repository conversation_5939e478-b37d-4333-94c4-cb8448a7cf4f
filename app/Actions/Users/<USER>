<?php

namespace App\Actions\Users;

use App\Actions\AnonymizePharmaceuticalServiceAction;
use App\Actions\AnonymizeVaccinationAction;
use App\CompanyUser;
use App\PharmaceuticalService;
use App\Shift;
use App\ShiftPlanGroupUser;
use App\User;
use App\UserPharmacyProfile;
use App\Vaccination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeletePharmacyEmployee
{
    public function execute(User $user): void
    {
        DB::beginTransaction();

        try {
            // 1. Delete shift plan related data
            ShiftPlanGroupUser::where('user_id', $user->id)->delete();

            // 2. Anonymize pharmaceutical services
            $pharmaceuticalServices = PharmaceuticalService::where('user_id', $user->id)->get();

            foreach ($pharmaceuticalServices as $service) {
                // Anonymize patient data
                // app(AnonymizePharmaceuticalServiceAction::class)->execute($service);

                // Remove user reference
                $service->update(['user_id' => null]);
            }

            // 3. Anonymize vaccinations
            $vaccinations = Vaccination::where('user_id', $user->id)->get();

            foreach ($vaccinations as $vaccination) {
                // Anonymize patient data
                // app(AnonymizeVaccinationAction::class)->execute($vaccination, true);

                // Remove user reference
                $vaccination->update(['user_id' => null]);
            }

            // 4. Clean up company user data
            CompanyUser::where('user_id', $user->id)->delete();

            UserPharmacyProfile::where('company_user_id', $user->id)
                ->update(['company_user_id' => null]);

            // 5. Clean up user profile data
            UserPharmacyProfile::where('user_id', $user->id)->delete();

            // 6. Delete at IDP
            if ($user->is_at_idp) {
                app(DeleteIDPUserAction::class)->deleteUser($user->uuid);
            }

            // 7. Clean up user data (existing functionality)
            $user->email = null;
            $user->username = null;
            $user->deleted_at = now();
            $user->save();

            DB::commit();

            Log::info('Successfully deleted pharmacy employee', [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete pharmacy employee', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
}
