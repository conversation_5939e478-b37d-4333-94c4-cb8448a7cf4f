<?php

namespace App\Actions\Kim;

use App\Enums\KimAddressStatus;
use App\Enums\KimVendorEnum;
use App\Events\Kim\KimAddressOrdered;
use App\Helper\Kim\KimAkquinetAddressApi;
use App\KimAddress;
use App\Mail\KimAddressOrderConfirmationMail;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CreateNewKimAddressAtAkquinet
{
    private KimAkquinetAddressApi $kimAddressAkquinetApi;

    public function __construct(KimAkquinetAddressApi $kimAddressAkquinetApi)
    {
        $this->kimAddressAkquinetApi = $kimAddressAkquinetApi;
    }

    public function execute(array $input, KimAddress $kimAddress, bool $notify = true): KimAddress
    {
        Gate::authorize('create', [KimAddress::class, $kimAddress->pharmacy]);

        if ($kimAddress->status !== KimAddressStatus::RESERVED->value) {
            throw new \Exception('Kim address is not reserved');
        }

        $endPart = Str::after($input['email'], '@');
        if ($endPart !== config('kim.vendors.'.KimVendorEnum::AKQUINET->value.'.domain')) {
            throw new \Exception('Kim address domain is not valid');
        }

        Validator::validate($input, [
            'tosCheckboxAccepted' => ['accepted'],
        ]);

        $kimAddress->update([
            'vendor' => KimVendorEnum::AKQUINET->value,
            'email' => $input['email'],
        ]);

        $response = $this->kimAddressAkquinetApi->createKimOrder($kimAddress)->json();

        $kimAddress->update([
            'additional' => array_merge($kimAddress->additional ?? [], [
                'customerNumber' => $response['subCustomer']['customerNumber'],
                'supportPin' => $response['subCustomer']['supportPin'],
                'voucherCode' => $response['createdRegistrationCodes'][0]['code'],
            ]),
            'status' => KimAddressStatus::ORDERED->value,
            'ordered_at' => now(),
        ]);

        if ($notify) {
            Mail::to($kimAddress->owner()->email)->send(new KimAddressOrderConfirmationMail($kimAddress));
        }

        event(new KimAddressOrdered($kimAddress));

        return $kimAddress;
    }
}
