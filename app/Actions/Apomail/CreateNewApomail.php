<?php

namespace App\Actions\Apomail;

use App\Apomail;
use App\Rules\GedisaMail;
use App\Rules\NotOnBlacklist;
use App\Rules\OnlyAllowedMailCharacters;
use App\Rules\OwnerMatches;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class CreateNewApomail
{
    public function execute(array $input, User $owner, bool $reserved = false): Apomail
    {
        Gate::forUser($owner)->authorize('reserve', Apomail::class);

        $input['owner_id'] = $owner->id;

        $validated = Validator::make($input, [
            'owner_id' => 'exists:user_pharmacy_profiles,user_id',
            'email' => ['required', app(GedisaMail::class)->onlyApomail(), 'unique:apomails', new OnlyAllowedMailCharacters, new NotOnBlacklist],
            'password' => [$reserved ? 'nullable' : 'required', Password::min(8)->letters()->numbers()->symbols()->mixedCase(), 'confirmed'],
            'attach.*' => [
                'integer',
                new OwnerMatches($owner->id),
            ],
            'autogenerated' => ['nullable', 'boolean'],
        ], [], [
            'email' => 'ApoMail-Adresse',
        ])->validate();

        $apomail = null;

        DB::transaction(function () use ($reserved, $validated, &$apomail) {
            $apomail = Apomail::forceCreate([
                'owner_id' => $validated['owner_id'],
                'email' => $validated['email'],
            ]);

            if (isset($validated['autogenerated'])) {
                $apomail->update([
                    'is_autogenerated' => $validated['autogenerated'],
                ]);
            }

            if (isset($validated['attach'])) {
                $apomail->attachUsers($validated['attach']);
            }

            if (! $reserved) {
                app(CreateNewApomailAccount::class)->execute($validated, $apomail);
            }
        });

        return $apomail;
    }
}
