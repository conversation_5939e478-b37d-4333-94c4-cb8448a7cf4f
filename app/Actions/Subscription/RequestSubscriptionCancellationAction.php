<?php

namespace App\Actions\Subscription;

use App\Domains\Subscription\Application\StripeProducts\BaseStripeProduct;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Pharmacy;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RequestSubscriptionCancellationAction
{
    public function execute(Pharmacy $pharmacy, User $user, ?string $reason = null): SubscriptionCancellation
    {
        return DB::transaction(function () use ($pharmacy, $user, $reason) {
            // Validierungen
            $this->validateCancellationRequest($pharmacy);

            // Berechne das Kündigungsdatum (nächstes Quartalsende)
            $effectiveDate = SubscriptionCancellation::calculateNextQuarterEnd();

            // Prüfe Mindestlaufzeit
            if (!$this->meetsMinimumRuntime($pharmacy)) {
                throw new SubscriptionCancellationException(
                    'Die Mindestlaufzeit von 3 Monaten wurde noch nicht erreicht.'
                );
            }

            // Prüfe ob buchbare Features aktiv sind
            if ($this->hasActiveBookableFeatures($pharmacy)) {
                throw new SubscriptionCancellationException(
                    'Es sind noch buchbare Features aktiv. Bitte kündigen Sie diese zuerst.'
                );
            }

            // Erstelle Kündigungsantrag
            $cancellation = SubscriptionCancellation::create([
                'pharmacy_id' => $pharmacy->id,
                'user_id' => $user->id,
                'status' => SubscriptionCancellationStatusEnum::PENDING,
                'requested_at' => now(),
                'effective_date' => $effectiveDate,
                'reason' => $reason,
                'metadata' => [
                    'subscription_start' => $pharmacy->subscription()?->created_at,
                    'current_products' => $this->getCurrentSubscriptionProducts($pharmacy),
                ],
            ]);

            // Log in Change History
            SubscriptionChangeHistory::log(
                pharmacy: $pharmacy,
                action: 'cancellation_requested',
                user: $user,
                newData: [
                    'effective_date' => $effectiveDate->toDateString(),
                    'reason' => $reason,
                ],
                notes: 'Kündigung über das Portal beantragt',
                source: 'web'
            );

            return $cancellation;
        });
    }

    private function validateCancellationRequest(Pharmacy $pharmacy): void
    {
        // Prüfe ob bereits eine aktive Kündigung existiert
        if ($pharmacy->subscriptionCancellation()->whereIn('status', [
            SubscriptionCancellationStatusEnum::PENDING,
            SubscriptionCancellationStatusEnum::CONFIRMED
        ])->exists()) {
            throw new SubscriptionCancellationException(
                'Es existiert bereits eine aktive Kündigung für diese Apotheke.'
            );
        }

        // Prüfe ob eine Basismitgliedschaft existiert
        if (!$pharmacy->isSubscribedToProduct(BaseStripeProduct::class)) {
            throw new SubscriptionCancellationException(
                'Es ist keine Basismitgliedschaft vorhanden, die gekündigt werden kann.'
            );
        }
    }

    private function meetsMinimumRuntime(Pharmacy $pharmacy): bool
    {
        $subscriptionStart = $pharmacy->subscription()?->created_at;
        
        if (!$subscriptionStart) {
            return false;
        }
        
        return $subscriptionStart->addMonths(3)->isPast();
    }

    private function hasActiveBookableFeatures(Pharmacy $pharmacy): bool
    {
        // Prüfe auf aktive KIM-Adressen
        if ($pharmacy->kimAddresses()->whereIn('status', ['reserved', 'ordered', 'active'])->exists()) {
            return true;
        }

        // Prüfe auf aktive CardLink-Bestellungen
        if ($pharmacy->cardLinkOrder()->whereNotIn('status', ['cancelled', 'completed'])->exists()) {
            return true;
        }

        // Prüfe auf aktive IA-Integration
        if ($pharmacy->integrations()->where('type', 'ia')->where('active', true)->exists()) {
            return true;
        }

        return false;
    }

    private function getCurrentSubscriptionProducts(Pharmacy $pharmacy): array
    {
        $subscription = $pharmacy->subscription();
        if (!$subscription) {
            return [];
        }

        return $subscription->items->map(function ($item) {
            return [
                'stripe_product' => $item->stripe_product,
                'stripe_price' => $item->stripe_price,
                'quantity' => $item->quantity,
            ];
        })->toArray();
    }
}

// Exception-Klasse für Kündigungsfehler
namespace App\Exceptions;

use Exception;

class SubscriptionCancellationException extends Exception
{
    //
}
