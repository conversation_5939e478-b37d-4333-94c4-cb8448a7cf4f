<?php

namespace App\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Mail\SubscriptionCancelledMail;
use App\Pharmacy;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Subscription;

class ExecuteSubscriptionCancellationAction
{
    public function execute(SubscriptionCancellation $cancellation): void
    {
        DB::transaction(function () use ($cancellation) {
            if ($cancellation->status !== SubscriptionCancellationStatusEnum::CONFIRMED) {
                throw new SubscriptionCancellationException(
                    'Nur bestätigte Kündigungen können ausgeführt werden.'
                );
            }

            if ($cancellation->effective_date->isFuture()) {
                throw new SubscriptionCancellationException(
                    'Die Kündigung kann erst am Stichtag ausgeführt werden.'
                );
            }

            $pharmacy = $cancellation->pharmacy;
            
            // Stripe-Abonnement kündigen
            $this->cancelStripeSubscriptions($pharmacy);

            // Kündigung als ausgeführt markieren
            $cancellation->update([
                'status' => SubscriptionCancellationStatusEnum::EXECUTED,
                'executed_at' => now(),
            ]);

            // Log in Change History
            SubscriptionChangeHistory::log(
                pharmacy: $pharmacy,
                action: 'cancellation_executed',
                user: null, // System-Aktion
                oldData: [
                    'status' => SubscriptionCancellationStatusEnum::CONFIRMED->value,
                ],
                newData: [
                    'status' => SubscriptionCancellationStatusEnum::EXECUTED->value,
                    'executed_at' => now()->toDateTimeString(),
                ],
                notes: 'Kündigung automatisch ausgeführt',
                source: 'system'
            );

            // E-Mail-Benachrichtigung senden
            $this->sendCancellationExecutedNotification($pharmacy, $cancellation);
        });
    }

    private function cancelStripeSubscriptions(Pharmacy $pharmacy): void
    {
        $pharmacy->subscriptions()
            ->active()
            ->each(function (Subscription $subscription) {
                // Kündigung zum Ende der aktuellen Abrechnungsperiode
                $subscription->cancel();
            });
    }

    private function sendCancellationExecutedNotification(Pharmacy $pharmacy, SubscriptionCancellation $cancellation): void
    {
        $emails = collect();
        
        // Rechnungsadresse
        if ($pharmacy->billingAddress?->email) {
            $emails->push($pharmacy->billingAddress->email);
        }
        
        // Inhaber und Subinhaber
        $pharmacy->ownersAndSubowners()
            ->pluck('email')
            ->each(fn ($email) => $emails->push($email));

        $emails->unique()->each(function ($email) use ($pharmacy, $cancellation) {
            Mail::to($email)->send(new SubscriptionCancelledMail($pharmacy, $cancellation));
        });
    }
}
