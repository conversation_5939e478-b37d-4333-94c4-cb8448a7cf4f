<?php

namespace App\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Pharmacy;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use App\User;
use Illuminate\Support\Facades\DB;

class RevokeSubscriptionCancellationAction
{
    public function execute(Pharmacy $pharmacy, User $user, ?string $reason = null): SubscriptionCancellation
    {
        return DB::transaction(function () use ($pharmacy, $user, $reason) {
            $cancellation = $pharmacy->subscriptionCancellation()
                ->whereIn('status', [
                    SubscriptionCancellationStatusEnum::PENDING,
                    SubscriptionCancellationStatusEnum::CONFIRMED
                ])
                ->first();

            if (!$cancellation) {
                throw new SubscriptionCancellationException(
                    'Es wurde keine aktive Kündigung gefunden, die widerrufen werden kann.'
                );
            }

            if (!$cancellation->canBeRevoked()) {
                throw new SubscriptionCancellationException(
                    'Diese <PERSON> kann nicht mehr widerrufen werden.'
                );
            }

            $oldData = [
                'status' => $cancellation->status->value,
                'effective_date' => $cancellation->effective_date->toDateString(),
            ];

            // Kündigung widerrufen
            $cancellation->update([
                'status' => SubscriptionCancellationStatusEnum::REVOKED,
                'revoked_at' => now(),
            ]);

            // Log in Change History
            SubscriptionChangeHistory::log(
                pharmacy: $pharmacy,
                action: 'cancellation_revoked',
                user: $user,
                oldData: $oldData,
                newData: [
                    'status' => SubscriptionCancellationStatusEnum::REVOKED->value,
                    'revoked_at' => now()->toDateTimeString(),
                ],
                notes: $reason ? "Grund: {$reason}" : 'Kündigung widerrufen',
                source: 'web'
            );

            return $cancellation;
        });
    }
}
