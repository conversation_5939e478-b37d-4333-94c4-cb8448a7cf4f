<?php

namespace App\Actions\IhreApotheken;

use App\Enums\Ia\IaRoleEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Support\Facades\DB;

class SetPermissionsForIaRoleAction
{
    public function execute(User $user, Pharmacy $pharmacy, IaRoleEnum $role): void
    {
        DB::transaction(function () use ($role, $user, $pharmacy) {
            $existingPermissions = $user
                ->pharmacies()
                ->wherePivot('pharmacy_id', $pharmacy->id)
                ->first()
                ?->pivot
                ->permissions ?? [];

            $permissions = collect([$role])
                ->map(fn ($permission) => (int) $permission->value)
                ->merge($existingPermissions)
                ->unique()
                ->sort()
                ->values()
                ->toArray();

            $pharmacy->users()->updateExistingPivot($user->id, [
                'permissions' => $permissions,
            ]);
        });
    }
}
