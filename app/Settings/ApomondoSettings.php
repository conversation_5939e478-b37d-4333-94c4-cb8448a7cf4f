<?php

namespace App\Settings;

use Illuminate\Support\Facades\File;
use RuntimeException;
use <PERSON><PERSON>\LaravelSettings\Settings;

class ApomondoSettings extends Settings
{
    public static function group(): string
    {
        return 'apomondo';
    }

    public function jwtIssuer(): string
    {
        $domain = parse_url(url()->full(), PHP_URL_HOST);

        if ($domain) {
            return $domain;
        }

        throw new RuntimeException(sprintf('Domain kann nicht aus der URL [%s] geparsed werden.', url()->full()));
    }

    public function jwtAudience(): string
    {
        $config = config('services.apomondo.audience');
        if (! is_string($config)) {
            throw new RuntimeException('Invalid configuration for apomondo audience');
        }

        return $config;
    }

    public function privateKey(): string
    {
        $privateKeyName = config('services.apomondo.private_key_path');
        if (! is_string($privateKeyName)) {
            throw new RuntimeException('Invalid configuration for apomondo private key name');
        }

        return File::get($privateKeyName) ?: throw new RuntimeException('Private key not found');
    }

    public function pharmaciesIssuer(): string
    {
        $config = config('services.apomondo.pharmacies_issuer');
        if (! is_string($config)) {
            throw new RuntimeException('Invalid configuration for apomondo pharmacies issuer');
        }

        return $config;
    }

    public function pharmaciesAudience(): string
    {
        $config = config('services.apomondo.pharmacies_audience');
        if (! is_string($config)) {
            throw new RuntimeException('Invalid configuration for apomondo pharmacies audience');
        }

        return $config;
    }

    public function pharmaciesPrivateKey(): string
    {
        $privateKeyName = config('services.apomondo.pharmacies_private_key_path');
        if (! is_string($privateKeyName)) {
            throw new RuntimeException('Invalid configuration for apomondo pharmacies private key name');
        }

        return File::get($privateKeyName) ?: throw new RuntimeException('Private key not found');
    }
}
