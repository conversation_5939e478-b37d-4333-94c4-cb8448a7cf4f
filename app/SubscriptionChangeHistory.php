<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property int|null $user_id
 * @property string $action
 * @property array|null $old_data
 * @property array|null $new_data
 * @property string|null $notes
 * @property string $source
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Pharmacy $pharmacy
 * @property User|null $user
 */
class SubscriptionChangeHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'user_id',
        'action',
        'old_data',
        'new_data',
        'notes',
        'source',
    ];

    protected $casts = [
        'old_data' => 'array',
        'new_data' => 'array',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Erstellt einen neuen Change History Eintrag
     */
    public static function log(
        Pharmacy $pharmacy,
        string $action,
        ?User $user = null,
        ?array $oldData = null,
        ?array $newData = null,
        ?string $notes = null,
        string $source = 'web'
    ): self {
        return self::create([
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user?->id,
            'action' => $action,
            'old_data' => $oldData,
            'new_data' => $newData,
            'notes' => $notes,
            'source' => $source,
        ]);
    }

    /**
     * Formatiert die Aktion für die Anzeige
     */
    public function getFormattedActionAttribute(): string
    {
        return match ($this->action) {
            'cancellation_requested' => 'Kündigung beantragt',
            'cancellation_confirmed' => 'Kündigung bestätigt',
            'cancellation_revoked' => 'Kündigung widerrufen',
            'cancellation_executed' => 'Kündigung ausgeführt',
            'association_change_with_cancellation' => 'Verbandswechsel mit Kündigung',
            default => $this->action,
        };
    }

    /**
     * Gibt den Benutzer-Namen zurück oder "System" falls kein Benutzer
     */
    public function getActorNameAttribute(): string
    {
        return $this->user ? $this->user->name : 'System';
    }
}
