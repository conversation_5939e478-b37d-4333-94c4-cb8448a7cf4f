<?php

namespace App\Livewire\Subscription;

use App\Actions\Subscription\RequestSubscriptionCancellationAction;
use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Pharmacy;
use App\SubscriptionCancellation;
use Livewire\Component;

class CancellationManager extends Component
{
    public Pharmacy $pharmacy;
    public ?SubscriptionCancellation $cancellation = null;
    
    // Modal states
    public bool $showCancellationModal = false;
    public bool $showRevokeModal = false;
    
    // Form data
    public string $cancellationReason = '';
    public string $revokeReason = '';
    public bool $confirmCancellation = false;
    public bool $confirmRevoke = false;

    protected $rules = [
        'cancellationReason' => 'nullable|string|max:1000',
        'revokeReason' => 'nullable|string|max:1000',
        'confirmCancellation' => 'required|accepted',
        'confirmRevoke' => 'required|accepted',
    ];

    protected $messages = [
        'confirmCancellation.accepted' => 'Sie müssen der Kündigung zustimmen.',
        'confirmRevoke.accepted' => 'Sie müssen dem Widerruf zustimmen.',
    ];

    public function mount(): void
    {
        $this->loadCancellation();
    }

    public function loadCancellation(): void
    {
        $this->cancellation = $this->pharmacy->subscriptionCancellation()
            ->whereIn('status', [
                SubscriptionCancellationStatusEnum::PENDING,
                SubscriptionCancellationStatusEnum::CONFIRMED
            ])
            ->first();
    }

    public function openCancellationModal(): void
    {
        $this->reset(['cancellationReason', 'confirmCancellation']);
        $this->showCancellationModal = true;
    }

    public function closeCancellationModal(): void
    {
        $this->showCancellationModal = false;
        $this->reset(['cancellationReason', 'confirmCancellation']);
    }

    public function openRevokeModal(): void
    {
        $this->reset(['revokeReason', 'confirmRevoke']);
        $this->showRevokeModal = true;
    }

    public function closeRevokeModal(): void
    {
        $this->showRevokeModal = false;
        $this->reset(['revokeReason', 'confirmRevoke']);
    }

    public function requestCancellation(): void
    {
        $this->validate([
            'cancellationReason' => 'nullable|string|max:1000',
            'confirmCancellation' => 'required|accepted',
        ]);

        try {
            app(RequestSubscriptionCancellationAction::class)->execute(
                $this->pharmacy,
                auth()->user(),
                $this->cancellationReason ?: null
            );

            $this->loadCancellation();
            $this->closeCancellationModal();
            
            session()->flash('success', 'Ihre Kündigung wurde erfolgreich beantragt.');
            
        } catch (SubscriptionCancellationException $e) {
            $this->addError('cancellation', $e->getMessage());
        } catch (\Exception $e) {
            $this->addError('cancellation', 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        }
    }

    public function revokeCancellation(): void
    {
        $this->validate([
            'revokeReason' => 'nullable|string|max:1000',
            'confirmRevoke' => 'required|accepted',
        ]);

        try {
            app(RevokeSubscriptionCancellationAction::class)->execute(
                $this->pharmacy,
                auth()->user(),
                $this->revokeReason ?: null
            );

            $this->loadCancellation();
            $this->closeRevokeModal();
            
            session()->flash('success', 'Ihre Kündigung wurde erfolgreich widerrufen.');
            
        } catch (SubscriptionCancellationException $e) {
            $this->addError('revoke', $e->getMessage());
        } catch (\Exception $e) {
            $this->addError('revoke', 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        }
    }

    public function getCanRequestCancellationProperty(): bool
    {
        return !$this->cancellation && $this->pharmacy->subscribed();
    }

    public function getCanRevokeCancellationProperty(): bool
    {
        return $this->cancellation && $this->cancellation->canBeRevoked();
    }

    public function render()
    {
        return view('livewire.subscription.cancellation-manager');
    }
}
