<?php

namespace App\Livewire\Association\Vaccinate\Components;

use App\Association;
use App\Enums\PharmacyVaccinateStatusEnum;
use App\Helper\AssociationsHelper;
use App\InfluenzaVaccination;
use App\Pharmacy;
use Exception;
use Livewire\Component;

class WaitingForApprovalList extends Component
{
    /** @var Association */
    public $association;

    /** @var bool */
    public $processing = false;

    public function render()
    {
        return view('livewire.association.vaccinate.components.waiting-for-approval-list', [
            'pharmacies' => AssociationsHelper::getPharmaciesRequestedToVaccinate($this->association->id),
        ]);
    }

    public function accept(Pharmacy $pharmacy)
    {
        $this->setStatus($pharmacy, PharmacyVaccinateStatusEnum::ACTIVE);
        $this->notify(__('notifications.vaccination-list.accept'));
    }

    public function decline(Pharmacy $pharmacy)
    {
        $this->setStatus($pharmacy, PharmacyVaccinateStatusEnum::INACTIVE);
        $this->notify(__('notifications.vaccination-list.declined'));
    }

    private function setStatus($pharmacy, $status)
    {
        if ($this->processing) {
            return;
        }

        $this->processing = true;

        try {
            switch ($status) {
                case PharmacyVaccinateStatusEnum::ACTIVE:
                    if (! user()->can('activate', [InfluenzaVaccination::class, $pharmacy])) {
                        abort(403);
                    }

                    break;

                case PharmacyVaccinateStatusEnum::INACTIVE:
                    if (! user()->can('deactivate', [InfluenzaVaccination::class, $pharmacy])) {
                        abort(403);
                    }

                    break;

                default:
                    abort(403);
            }

            $pharmacy->settings->vaccinate_status = $status;
            $pharmacy->settings->save();
        } catch (Exception $exception) {
            // TODO
        } finally {
            $this->processing = false;
        }
    }
}
