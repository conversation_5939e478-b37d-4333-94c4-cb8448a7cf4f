<?php

namespace App\Livewire;

use App\HealthInsuranceCompany;
use App\InstitutionIdentifier;
use App\Vaccination;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class HealthInsuranceChoice extends Component
{
    public bool $isPrivate = false;

    public int $value = 0;

    public bool $healthInsuranceType;

    /** @var array<array<string, int|string|null>> */
    public array $options = [];

    private ?Vaccination $vaccination = null;

    /**
     * @var array<string, string>
     */
    protected $listeners = [
        'update-health-insurance-choices' => 'updateOptions',
    ];

    public function mount(?Vaccination $vaccination = null): void
    {
        $this->vaccination = $vaccination;
        /** @var int $value */
        $value = old(
            'health_insurance_company_id',
            $this->vaccination?->health_insurance_company_id ?? 0
        );
        $this->value = $value;
        $this->isPrivate = (bool) old(
            'insurance_type',
            $this->vaccination?->healthInsuranceCompany->is_private ?? false
        );
        $this->updateOptions('');
    }

    public function render(): View
    {
        return view('livewire.health-insurance-choice');
    }

    public function updateOptions(string $pattern): void
    {
        $this->options = [];

        if (is_numeric($pattern)) {
            $institutionIdentifiers = InstitutionIdentifier::with('healthInsuranceCompany')
                ->whereRelation('healthInsuranceCompany', 'is_private', $this->isPrivate)
                ->where('identifier', 'like', '%'.$pattern.'%')
                ->get();

            foreach ($institutionIdentifiers as $institutionIdentifier) {
                $this->options[] = [
                    'value' => $institutionIdentifier->healthInsuranceCompany?->id,
                    'label' => $institutionIdentifier->identifier.' - '.$institutionIdentifier->healthInsuranceCompany?->name,
                ];
            }
        } else {
            foreach (
                HealthInsuranceCompany::with('institutionIdentifiers')
                    ->where('is_private', (int) $this->isPrivate)
                    ->where('name', 'like', '%'.$pattern.'%')
                    ->get() as $healthInsuranceCompany
            ) {
                $this->options[] = [
                    'value' => $healthInsuranceCompany->id,
                    'label' => $healthInsuranceCompany->name,
                ];
            }
        }
    }

    public function updatedIsPrivate(): void
    {
        $this->value = 0;
        $this->updateOptions('');
    }
}
