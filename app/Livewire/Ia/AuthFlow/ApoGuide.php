<?php

namespace App\Livewire\Ia\AuthFlow;

use App\Helper\IaHelper;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Traits\InteractsWithSession;
use Exception;
use Illuminate\Contracts\View\View;

class ApoGuide extends WizardStep
{
    use InteractsWithSession;

    public string $title = 'ApoGuide aktivieren';

    public ?string $subtitle = 'Freischaltung Ihrer Apotheke für Apothekensuche in ApoGuide.';

    public ?string $nextStep = RolesAndPermissions::class;

    public ?string $prevStep = MergeData::class;

    public Pharmacy $pharmacy;

    public bool $show_in_apoguide = true;

    public function mount(): void
    {
        try {
            $this->pharmacy = IaHelper::getMatchedPharmacy();
        } catch (Exception $e) {
            abort(403);
        }
    }

    public function submit(): void
    {
        $this->pharmacy->show_in_apoguide = $this->show_in_apoguide;
        $this->pharmacy->save();

        $this->next();
    }

    protected function sessionPrefix(): ?string
    {
        return IaHelper::wizardSessionPrefix();
    }

    public function render(): View
    {
        return view('livewire.ia.auth-flow.apoguide');
    }
}
