<?php

namespace App\Domains\Subscription\Application\StripeProducts\OneTimePurchases;

use App\Domains\Payment\Domain\Data\StripeProductFrontendTextData;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\Settings\Products\CardLinkPacketMPlusProductSetting;
use App\Domains\Subscription\Application\Settings\Products\StripeProductSetting;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Pharmacy;

class CardLinkPacketMPlusStripeProduct extends StripeProduct
{
    public function getSettings(): StripeProductSetting
    {
        return app(CardLinkPacketMPlusProductSetting::class);
    }

    public function getPublicRepresentationData(Pharmacy $pharmacy): StripePublicRepresentationData
    {
        return new StripePublicRepresentationData(
            class: self::class,
            price: $this->getOneTimeStripePrice($pharmacy)->price,
            text: new StripeProductFrontendTextData(
                name: 'CardLink Paket M+',
                description: '250 weitere Transaktionen',
                features: []
            )
        );
    }
}
