<?php

namespace App\Domains\Subscription\Application\Settings\Discounts;

use App\Domains\Subscription\Application\Discounts\StripeDiscount;
use App\Enums\SystemSettings\SystemSettingGroupEnum;
use Spa<PERSON>\LaravelSettings\Settings;

abstract class StripeDiscountSetting extends Settings
{
    public string $discount_id;

    abstract public static function getStripeDiscount(): StripeDiscount;

    public static function group(): string
    {
        return SystemSettingGroupEnum::STRIPE_PRICES->value.'-'.static::getStripeDiscount()::class;
    }
}
