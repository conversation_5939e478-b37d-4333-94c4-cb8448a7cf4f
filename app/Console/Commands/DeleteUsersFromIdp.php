<?php

namespace App\Console\Commands;

use App\Actions\Users\DeleteIDPUserAction;
use App\Jobs\DeleteUserAtIdp;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DeleteUsersFromIdp extends Command
{
    /**
     * @var string
     */
    protected $signature = 'user:delete-multiple-at-idp {--update-since=}';

    /**
     * @var string
     */
    protected $description = 'Delete multiple users at IDP';

    public function handle()
    {
        if ($date = $this->option('update-since')) {
            $date = Carbon::parse($date);
        }

        $deleteIDPUserAction = app(DeleteIDPUserAction::class);

        User::query()
            ->where('is_at_idp', true)
            ->when($date, fn ($q) => $q->where('updated_at', '>', $date))
            ->eachById(function (User $user) {
                DeleteUserAtIdp::dispatch($user)->onQueue('idp');

                $this->info('Deleting User '.$user->id);
            });
    }
}
