<?php

namespace App\Enums\Vaccinate;

use Illuminate\Support\Collection;

class SevereVaccinationReactionsEnum
{
    const ANAPHYLACTIC_REACTION = 1;

    const EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM = 2;

    const EMERGENCY_SITUATION_RESPIRATORY_SYSTEM = 3;

    const NONE = 4;

    public static function getForDropdown()
    {
        return collect([
            self::ANAPHYLACTIC_REACTION => self::ANAPHYLACTIC_REACTION,
            self::EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM => self::EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM,
            self::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM => self::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM,
            self::NONE => self::NONE,
        ]);
    }

    public static function getForDropdownWithoutNone()
    {
        return collect([
            self::ANAPHYLACTIC_REACTION => self::ANAPHYLACTIC_REACTION,
            self::EMER<PERSON>NCY_SITUATION_CARDIOVASCULAR_SYSTEM => self::EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM,
            self::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM => self::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM,
        ]);
    }

    public static function getAll(): Collection
    {
        return collect([
            self::ANAPHYLACTIC_REACTION,
            self::EMERGENCY_SITUATION_CARDIOVASCULAR_SYSTEM,
            self::EMERGENCY_SITUATION_RESPIRATORY_SYSTEM,
            self::NONE,
        ]);
    }
}
