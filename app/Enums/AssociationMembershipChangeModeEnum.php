<?php

namespace App\Enums;

use App\Traits\BackedEnumToArray;

enum AssociationMembershipChangeModeEnum: string
{
    use BackedEnumToArray;

    case ADD = 'add';
    case CHANGE = 'change';
    case DELETE = 'delete';
    case REVERT = 'revert';

    public function forNova(): string
    {
        return match ($this) {
            self::ADD => 'Verbandsmitgliedschaft hinzugefügt',
            self::CHANGE => 'Verbandsmitgliedschaft geändert',
            self::DELETE => 'Verbandsmitgliedschaft gekündigt',
            self::REVERT => 'Änderung an Verbandsmitgliedschaft rückgängig gemacht',
        };
    }
}
