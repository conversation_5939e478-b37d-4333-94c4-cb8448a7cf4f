<?php

namespace App\Enums;

use Illuminate\Support\Collection;

class YesNoUnknownEnum
{
    const YES = 0;

    const NO = 1;

    const UNKNOWN = 2;

    public static function getAll(): Collection
    {
        return collect([
            self::YES,
            self::NO,
            self::UNKNOWN,
        ]);
    }

    public static function getLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            case self::YES:
                return trans('messages.yes');
            case self::NO:
                return trans('messages.no');
            case self::UNKNOWN:
                return trans('messages.unknown');
        }

        return '';
    }

    public static function getForDropdown()
    {
        return [
            self::YES => self::getLabel(self::YES),
            self::NO => self::getLabel(self::NO),
            self::UNKNOWN => self::getLabel(self::UNKNOWN),
        ];
    }
}
