<?php

namespace App;

use App\Enums\SubscriptionCancellationStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property int $user_id
 * @property SubscriptionCancellationStatusEnum $status
 * @property Carbon $requested_at
 * @property Carbon $effective_date
 * @property Carbon|null $confirmed_at
 * @property Carbon|null $revoked_at
 * @property Carbon|null $executed_at
 * @property string|null $reason
 * @property array|null $metadata
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Pharmacy $pharmacy
 * @property User $user
 */
class SubscriptionCancellation extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'user_id',
        'status',
        'requested_at',
        'effective_date',
        'confirmed_at',
        'revoked_at',
        'executed_at',
        'reason',
        'metadata',
    ];

    protected $casts = [
        'status' => SubscriptionCancellationStatusEnum::class,
        'requested_at' => 'datetime',
        'effective_date' => 'datetime',
        'confirmed_at' => 'datetime',
        'revoked_at' => 'datetime',
        'executed_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Prüft ob die Kündigung noch widerrufen werden kann
     */
    public function canBeRevoked(): bool
    {
        return in_array($this->status, [
            SubscriptionCancellationStatusEnum::PENDING,
            SubscriptionCancellationStatusEnum::CONFIRMED
        ]) && $this->effective_date->isFuture();
    }

    /**
     * Prüft ob die Kündigung wirksam ist
     */
    public function isEffective(): bool
    {
        return $this->status === SubscriptionCancellationStatusEnum::EXECUTED
            || ($this->status === SubscriptionCancellationStatusEnum::CONFIRMED && $this->effective_date->isPast());
    }

    /**
     * Berechnet das nächste Quartalsende für die Kündigung
     */
    public static function calculateNextQuarterEnd(Carbon $from = null): Carbon
    {
        $date = $from ?? now();
        
        // Nächstes Quartalsende finden
        $quarter = ceil($date->month / 3);
        $year = $date->year;
        
        // Wenn wir bereits im letzten Monat des Quartals sind, nächstes Quartal nehmen
        if ($date->month % 3 === 0 && $date->day > 1) {
            $quarter++;
            if ($quarter > 4) {
                $quarter = 1;
                $year++;
            }
        }
        
        $endMonth = $quarter * 3;
        return Carbon::create($year, $endMonth, 1)->endOfMonth();
    }

    /**
     * Prüft ob die Mindestlaufzeit von 3 Monaten erfüllt ist
     */
    public function meetsMinimumRuntime(): bool
    {
        $subscriptionStart = $this->pharmacy->subscription()?->created_at;
        
        if (!$subscriptionStart) {
            return true; // Wenn kein Abo-Start gefunden wird, erlauben wir die Kündigung
        }
        
        return $subscriptionStart->addMonths(3)->isPast();
    }
}
