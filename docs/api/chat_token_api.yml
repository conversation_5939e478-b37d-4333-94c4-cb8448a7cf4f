openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'Token API'

components:
  schemas:
    ChatToken:
      type: object
      description: 'JWT token structure of the decoded Chat Token'
      properties:
        iss:
          type: string
          description: 'Token issuer'
        iat:
          type: integer
          description: 'Issued at timestamp'
        exp:
          type: integer
          description: 'Expiration timestamp (60 minutes from issuance)'
        aud:
          type: string
          description: 'Token audience'
        sub:
          type: string
          description: 'User ID'
        user_id:
          type: string
          description: 'User ID'
        displayname:
          type: string
          description: 'User display name'

    PharmaciesToken:
      type: object
      description: 'JWT token structure of the decoded Pharmacies Token. Documentation: https://gedisa.atlassian.net/wiki/x/BgDTOw'
      properties:
        iss:
          type: string
          description: 'Token issuer'
        iat:
          type: integer
          description: 'Issued at timestamp'
        exp:
          type: integer
          description: 'Expiration timestamp (60 minutes from issuance)'
        aud:
          type: string
          description: 'Token audience'
        sub:
          type: string
          description: 'User UUID'
        permissions:
          type: object
          description: 'Map of pharmacy UUIDs to the users permissions within that pharmacy'
          additionalProperties:
            type: array
            items:
              type: string
              enum: [
                'view_pharmacy',
                'use_cardlink',
                'use_read_only_chat',
                'use_employee_chat',
                'use_patient_chat',
                'calendar_create_appointments',
                'calendar_update_appointments',
                'calendar_delete_appointments',
                'calendar_create_serial_appointments',
                'calendar_update_serial_appointments',
                'calendar_delete_serial_appointments',
                'calendar_confirm_appointments',
                'calendar_cancel_appointments',
                'calendar_index_appointments',
                'calendar_view_appointments',
                'calendar_index_serial_appointments',
                'terminmanagement_manager',
                'terminmanagement',
                'telepharmacy_create_serial_appointments',
                'telepharmacy_update_serial_appointments',
                'telepharmacy_delete_serial_appointments',
                'telepharmacy_index_serial_appointments',
                'telepharmacy_create_appointments',
                'telepharmacy_update_appointments',
                'telepharmacy_cancel_appointments',
                'telepharmacy_index_appointments',
                'telepharmacy_view_appointments',
                'telepharmacy_confirm_appointments',
                'telepharmacy_delete_appointments',
                'telepharmazie_manager',
                'telepharmazie'
              ]
        pharmacy_information:
          type: object
          description: 'Map of pharmacy UUIDs to the pharmacy information independent of the users permissions'
          additionalProperties:
              type: object
              properties:
                name:
                  type: string
                cardlink:
                  type: boolean
                calendar:
                    type: boolean
                telepharmacy:
                    type: boolean
                employee_chat:
                    type: boolean
                patient_chat:
                    type: boolean
        apomondo_token:
          type: string
          description: 'Nested JWT token usable for authentication with Apomondo'

    ErrorMessage:
      type: object
      properties:
        message:
          type: string

    TokenResponse:
      type: object
      properties:
        token:
          type: string
          description: 'JWT token'

paths:
  /api/v1/users/chat-token:
    get:
      summary: 'Get chat token'
      description: 'This endpoint is used to get the chat token for a user.'
      parameters:
        - in: 'header'
          name: 'Authorization'
          schema:
            type: 'string'
          required: true
          description: 'Bearer token for the user.'
      responses:
        '200':
          description: 'Successful operation'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'No Bearer Token provided'
        '401':
          description: 'Unauthorized'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                no_token:
                  value:
                    message: 'Token not provided.'
                expired:
                  value:
                    message: 'Provided token is expired.'
        '403':
          description: 'Forbidden'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                no_pharmacy:
                  value:
                    message: 'User has no pharmacy.'
                no_chat_allowed:
                  value:
                    message: 'User has pharmacies but none of them allows the user to chat.'
        '404':
          description: 'Not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'User not found.'

  /api/v1/users/pharmacies-token:
    get:
      summary: 'Get pharmacies token'
      description: 'This endpoint is used to get the pharmacies token for a user with permissions included.'
      parameters:
        - in: 'header'
          name: 'Authorization'
          schema:
            type: 'string'
          required: true
          description: 'Bearer token for the user.'
      responses:
        '200':
          description: 'Successful operation'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'No Bearer Token provided'
        '401':
          description: 'Unauthorized'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                no_token:
                  value:
                    message: 'Token not provided.'
                expired:
                  value:
                    message: 'Provided token is expired.'
        '404':
          description: 'Not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'User not found.'
