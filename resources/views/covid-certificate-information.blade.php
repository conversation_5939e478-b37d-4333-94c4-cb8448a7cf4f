@extends('layouts.guest')

@section('body')
    <div class="antialiased flex flex-col min-h-screen bg-gray-50">
        <header class="relative z-10">
            <div class="bg-gray-50">
                <nav class="relative max-w-7xl mx-auto flex items-center justify-between pt-8 px-4 sm:px-6 lg:px-8" aria-label="Global">
                    <div class="flex items-center justify-between w-full lg:w-auto">
                        <a href="{{ route('home') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 583.9 102" class="h-8 w-auto sm:h-10">
                                <path fill="#007283" d="M214 86.5V43.9h-42.5v14.2h28.3v14.2h-35.4c-3.9 0-7.1-3.2-7.1-7.1V36.9c0-3.9 3.2-7.1 7.1-7.1H214V15.6h-49.6c-11.7 0-21.3 9.5-21.3 21.3v28.3c0 11.7 9.5 21.3 21.3 21.3H214zm35.4 0H299V72.3h-49.6c-3.9 0-7.1-3.2-7.1-7.1v-7.1H299V43.9h-56.7v-7.1c0-3.9 3.2-7.1 7.1-7.1H299V15.6h-49.6c-11.7 0-21.3 9.5-21.3 21.3v28.3c.1 11.7 9.6 21.3 21.3 21.3zm120.5-21.3c0 3.9-3.2 7.1-7.1 7.1h-35.4V29.8h35.4c3.9 0 7.1 3.2 7.1 7.1v28.3zm14.2-.1V36.8c0-11.7-9.5-21.2-21.3-21.2h-49.6v70.9h49.6c11.8 0 21.3-9.6 21.3-21.4zm14.2 21.4h14.2V15.6h-14.2v70.9zm42.5-49.6c0-3.9 3.2-7.1 7.1-7.1h45.4V15.6h-45.4c-11.7 0-21.3 9.5-21.3 21.3s9.5 21.3 21.3 21.3h28.3c3.9 0 7.1 3.2 7.1 7.1s-3.2 7.1-7.1 7.1h-48.5v14.2h48.5c11.7 0 21.3-9.5 21.3-21.3 0-11.7-9.5-21.3-21.3-21.3h-28.3c-3.9-.1-7.1-3.2-7.1-7.1m127.5 0V44h-42.5v-7.1c0-3.9 3.2-7.1 7.1-7.1h28.3c4 0 7.1 3.1 7.1 7.1m14.2 7v-7.1c0-11.7-9.5-21.3-21.3-21.3h-28.3c-11.7 0-21.3 9.5-21.3 21.3v49.6h14.2V58.1h42.5v28.3h14.2V43.9z"></path>
                                <defs>
                                    <path id="SVGID_1_" d="M44.8 1.4s-9.2 0-9.2 9.2v15.8c0 9.2-9.2 9.2-9.2 9.2H10.6s-9.2 0-9.2 9.2v12.4s0 9.2 9.2 9.2H23s9.2 0 9.2-9.2V41.4c0-9.2 9.2-9.2 9.2-9.2h15.8s9.2 0 9.2-9.2V10.6s0-9.2-9.2-9.2H44.8z"></path>
                                </defs>
                                <clipPath id="SVGID_00000106861016002888113810000000584587579477106838_">
                                    <use xlink:href="#SVGID_1_" overflow="visible"></use>
                                </clipPath>
                                <linearGradient id="SVGID_00000033331311403497304600000002628790549673946263_" x1="-1234.494" x2="-1233.994" y1="-52.828" y2="-52.828" gradientTransform="rotate(-135.4 -49806.2 -22962.994) scale(82.1911)" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#761d18"></stop>
                                    <stop offset=".024" stop-color="#761d18"></stop>
                                    <stop offset=".322" stop-color="#a21114"></stop>
                                    <stop offset=".612" stop-color="#c50811"></stop>
                                    <stop offset=".847" stop-color="#db0210"></stop>
                                    <stop offset="1" stop-color="#e3000f"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_00000033331311403497304600000002628790549673946263_)" d="m34.4 98.9-65.5-64.5 64.6-65.5 65.4 64.6z" clip-path="url(#SVGID_00000106861016002888113810000000584587579477106838_)"></path>
                                <defs>
                                    <path id="SVGID_00000140733173457919040950000002702862302579008416_" d="M79 35.6s-9.2 0-9.2 9.2v15.8c0 9.2-9.2 9.2-9.2 9.2H44.8s-9.2 0-9.2 9.2v12.4s0 9.2 9.2 9.2h12.4s9.2 0 9.2-9.2V75.6c0-9.2 9.2-9.2 9.2-9.2h15.8s9.2 0 9.2-9.2V44.8s0-9.2-9.2-9.2H79z"></path>
                                </defs>
                                <clipPath id="SVGID_00000024722967554870221510000010034076439677423746_">
                                    <use xlink:href="#SVGID_00000140733173457919040950000002702862302579008416_" overflow="visible"></use>
                                </clipPath>
                                <linearGradient id="SVGID_00000142137702054127312720000006496359265246870185_" x1="-1234.712" x2="-1234.212" y1="-51.902" y2="-51.902" gradientTransform="rotate(-132.7 -47497.646 -23229.921) scale(78.4737)" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#761d18"></stop>
                                    <stop offset=".024" stop-color="#761d18"></stop>
                                    <stop offset=".322" stop-color="#a21114"></stop>
                                    <stop offset=".612" stop-color="#c50811"></stop>
                                    <stop offset=".847" stop-color="#db0210"></stop>
                                    <stop offset="1" stop-color="#e3000f"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_00000142137702054127312720000006496359265246870185_)" d="M65.5 133 3.2 65.5 70.7 3.2 133 70.7z" clip-path="url(#SVGID_00000024722967554870221510000010034076439677423746_)"></path>
                                <defs>
                                    <path id="SVGID_00000087402218477659940790000001466792377470850972_" d="M44.8 35.6s-9.2 0-9.2 9.2v12.4s0 9.2 9.2 9.2h12.4s9.2 0 9.2-9.2V44.8s0-9.2-9.2-9.2H44.8z"></path>
                                </defs>
                                <clipPath id="SVGID_00000180355061298882365660000015947003976628463007_">
                                    <use xlink:href="#SVGID_00000087402218477659940790000001466792377470850972_" overflow="visible"></use>
                                </clipPath>
                                <linearGradient id="SVGID_00000049219785901326810000000005632207708086787211_" x1="-1236.531" x2="-1236.031" y1="-52.394" y2="-52.394" gradientTransform="rotate(-134.7 -44012.56 -20597.281) scale(72.543)" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#761d18"></stop>
                                    <stop offset=".024" stop-color="#761d18"></stop>
                                    <stop offset=".322" stop-color="#a21114"></stop>
                                    <stop offset=".612" stop-color="#c50811"></stop>
                                    <stop offset=".847" stop-color="#db0210"></stop>
                                    <stop offset="1" stop-color="#e3000f"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_00000049219785901326810000000005632207708086787211_)" d="M50.9 81.8 20.2 50.9l31-30.7 30.6 31z" clip-path="url(#SVGID_00000180355061298882365660000015947003976628463007_)"></path>
                            </svg>
                        </a>
                    </div>
                </nav>
            </div>
        </header>

        <div class="flex-grow flex items-top justify-center py-4 sm:pt-0">
            <div class="content" style="width:100%;">
                <div class="bg-white">
                    <main>
                        <!-- Header -->
                        <div class="pt-24 bg-gray-50 sm:pt-32">
                            <div class="max-w-md mx-auto pl-4 pr-8 sm:max-w-lg sm:px-6 lg:max-w-7xl lg:px-8">
                                <h1 class="text-4xl leading-10 font-extrabold text-gray-900 text-center sm:text-5xl sm:leading-none lg:text-6xl">
                                    Datenauskunft
                                </h1>
                            </div>
                        </div>

                        <!-- Contact Section -->
                        <div class="bg-gray-50">
                            <div class="max-w-5xl mx-auto pt-16 px-4 sm:py-24 sm:px-6 lg:px-8">
                                <p class="mb-3">
                                    Die GEDISA hat den Betrieb des Service über die Auskunft zu Covid19-Impfzertifikaten
                                    zum 15.06.2024 aus rechtlichen Gründen, insbesondere aufgrund datenschutzrechtlicher
                                    Aspekte, eingestellt.
                                </p>
                                <p>
                                    Bitte beachten Sie, dass auch das E-Mailpostfach für den Versand der Auskünfte
                                    abgeschaltet ist und nicht mehr überwacht wird.
                                </p>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>

        <footer class="w-full max-w-md mx-auto py-12 px-4 sm:max-w-7xl sm:px-6 lg:py-16 lg:px-8">
            <div class="border-t border-slate-900/5 py-10">
                <p class="text-center text-sm leading-6 text-slate-500">© {{ now()->year }}   GEDISA - Gesellschaft für digitale Services der Apotheken mbH </p>
                <div class="mt-8 flex items-center justify-center space-x-4 font-semibold leading-6 text-slate-700">
                    <a href="{{ route('privacy') }}">Datenschutz</a>
                    <div class="h-4 w-px bg-slate-500/20"></div>
                    <a href="{{ route('imprint') }}">Impressum</a>
                </div>
            </div>
        </footer>
    </div>

    @include('layouts.cookie-notice')
@endsection
