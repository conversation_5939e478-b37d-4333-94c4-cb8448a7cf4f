@component('mail::message')
# Ihre Basismitgliedschaft wurde gekündigt

Liebe Damen und Herren,

Ihre Basismitgliedschaft für **{{ $pharmacy->name }}** wurde heute gekündigt.

## Details der Kündigung

- **Apotheke:** {{ $pharmacy->name }}
- **Gekündigt am:** {{ $cancellation->executed_at->format('d.m.Y H:i') }} Uhr
- **Ursprünglich beantragt am:** {{ $cancellation->requested_at->format('d.m.Y H:i') }} Uhr

@if($cancellation->reason)
**Angegebener Grund:** {{ $cancellation->reason }}
@endif

## Was bedeutet das für Sie?

- Ihre Basismitgliedschaft ist ab sofort beendet
- Der Zugang zu kostenpflichtigen Features wurde deaktiviert
- Ihre Daten bleiben für {{ config('subscription.data-access-period.duration', 120) }} Monate verfügbar
- Sie können weiterhin auf bestimmte Grundfunktionen zugreifen

## Datenzugang nach der Kündigung

Folgende Bereiche bleiben Ihnen zugänglich:
@foreach(config('subscription.data-access-period.allowed-routes', []) as $route)
- {{ $route }}
@endforeach

## Erneute Anmeldung

Sie können jederzeit eine neue Basismitgliedschaft abschließen. Kontaktieren Sie uns gerne, wenn Sie Fragen haben oder sich erneut anmelden möchten.

@component('mail::button', ['url' => route('pharmacies.subscription', $pharmacy)])
Zum Portal
@endcomponent

Vielen Dank für Ihr Vertrauen in der Vergangenheit.

Mit freundlichen Grüßen,<br>
Ihr {{ config('app.name') }} Team
@endcomponent
