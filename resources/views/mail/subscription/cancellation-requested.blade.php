@component('mail::message')
# Kündigungsbestätigung

Liebe Damen und Herren,

wir haben Ihre Kündigung der Basismitgliedschaft für **{{ $pharmacy->name }}** erhalten.

## Details Ihrer Kündigung

- **Apotheke:** {{ $pharmacy->name }}
- **Beantragt am:** {{ $cancellation->requested_at->format('d.m.Y H:i') }} Uhr
- **Kündigungsdatum:** {{ $cancellation->effective_date->format('d.m.Y') }}
- **Status:** {{ $cancellation->status->label() }}

@if($cancellation->reason)
**Angegebener Grund:** {{ $cancellation->reason }}
@endif

## Was passiert als nächstes?

Ihre Kündigung wird zum **{{ $cancellation->effective_date->format('d.m.Y') }}** wirksam. Bis zu diesem Datum können Sie alle Funktionen Ihrer Basismitgliedschaft weiterhin nutzen.

**Wichtige Hinweise:**
- Nach Ausspruch der Kündigung können keine neuen Features mehr aktiviert werden
- Sie können die Kündigung bis zum Stichtag widerrufen
- Bereits gebuchte Features (KIM, CardLink, IA) müssen separat gekündigt werden

## Kündigung widerrufen

Falls Sie Ihre Meinung ändern, können Sie die Kündigung jederzeit bis zum {{ $cancellation->effective_date->format('d.m.Y') }} in Ihrem Portal widerrufen.

@component('mail::button', ['url' => route('pharmacies.subscription', $pharmacy)])
Zum Portal
@endcomponent

Bei Fragen stehen wir Ihnen gerne zur Verfügung.

Mit freundlichen Grüßen,<br>
Ihr {{ config('app.name') }} Team
@endcomponent
