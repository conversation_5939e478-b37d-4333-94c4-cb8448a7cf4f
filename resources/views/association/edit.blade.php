@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                Verband bearbeiten
            </x-slot>
            <x-slot name="action">

            </x-slot>
        </x:header>
        @if ($errors->any())
            <x:row>
                <x:col>
                    <div>
                        <x:alert-validation-error />
                    </div>
                </x:col>
            </x:row>
        @endif

        <form method="POST">
            @csrf
            @method('PUT')
            <x:card>
                <x:row>
                    <x:col>
                        <x:input.text
                                label="Adresszusatz"
                                name="optional_address_line"
                                :value="old('optional_address_line', $association->optional_address_line)"
                                :selected="old('optional_address_line', $association->optional_address_line)"
                                :error="$errors->first('optional_address_line')"
                        />
                    </x:col>
                </x:row>
                <x:row>
                    <x:col class="md:w-2/3">
                        <x:input.text
                            label="Straße"
                            name="street"
                            :value="old('street', $association->street)"
                            :selected="old('street', $association->street)"
                            :error="$errors->first('street')"
                        />
                    </x:col>
                    <x:col class="md:w-1/3">
                        <x:input.text
                            label="Hausnummer"
                            name="house_number"
                            :value="old('house_number', $association->house_number)"
                            :error="$errors->first('house_number')"
                        />
                    </x:col>
                </x:row>
                <x:row class="mt-4">
                    <x:col class="md:w-1/3">
                        <x:input.text
                            label="Postleitzahl"
                            name="postcode"
                            :value="old('postcode', $association->postcode)"
                            :error="$errors->first('postcode')"
                        />
                    </x:col>
                    <x:col class="md:w-2/3">
                        <x:input.text
                            label="Ort"
                            name="city"
                            :value="old('city', $association->city)"
                            :error="$errors->first('city')"
                        />
                    </x:col>

                </x:row>
                <x:row class="mt-4">
                    <x:col class="">
                        <x:input.text
                            label="Name des Datenschützers"
                            name="privacy_person_name"
                            :value="old('privacy_person_name', $association->privacy_person_name)"
                            :error="$errors->first('privacy_person_name')"
                        />
                    </x:col>
                </x:row>
            </x:card>

            <div class="mt-12 w-full flex justify-center">
                <x-button size="lg">
                    Speichern
                </x-button>
            </div>
        </form>
    </x:content>
@endsection
