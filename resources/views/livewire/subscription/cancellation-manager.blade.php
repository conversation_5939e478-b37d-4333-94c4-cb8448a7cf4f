<div class="space-y-6">
    {{-- Success Message --}}
    @if (session()->has('success'))
        <x:alert type="success">
            <x:slot name="description">
                {{ session('success') }}
            </x:slot>
        </x:alert>
    @endif

    {{-- Current Cancellation Status --}}
    @if ($cancellation)
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-orange-800">
                        Kündigung {{ $cancellation->status->label() }}
                    </h3>
                    <div class="mt-2 text-sm text-orange-700">
                        <p>{{ $cancellation->status->description() }}</p>
                        <p class="mt-1">
                            <strong>Kündigungsdatum:</strong> {{ $cancellation->effective_date->format('d.m.Y') }}
                        </p>
                        @if ($cancellation->reason)
                            <p class="mt-1">
                                <strong>Grund:</strong> {{ $cancellation->reason }}
                            </p>
                        @endif
                    </div>
                    
                    @if ($cancellation && $cancellation->canBeRevoked())
                        <div class="mt-4">
                            <button
                                wire:click="openRevokeModal"
                                type="button"
                                class="bg-white px-3 py-2 border border-orange-300 rounded-md text-sm font-medium text-orange-700 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            >
                                Kündigung widerrufen
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    {{-- Cancellation Request Section --}}
    @if (!$cancellation && $pharmacy->subscribed())
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basismitgliedschaft kündigen</h3>
            
            <div class="space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-800">Wichtige Informationen zur Kündigung</h4>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li><strong>Mindestlaufzeit:</strong> 3 Monate</li>
                                    <li><strong>Kündigungsfrist:</strong> 1 Monat zum Quartalsende</li>
                                    <li><strong>Nächstmögliches Kündigungsdatum:</strong> {{ \App\SubscriptionCancellation::calculateNextQuarterEnd()->format('d.m.Y') }}</li>
                                    <li>Die Kündigung gilt für Basis- und Plus-Verbandsnutzer</li>
                                    <li>Nach der Kündigung können keine neuen Features aktiviert werden</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <button 
                    wire:click="openCancellationModal"
                    type="button" 
                    class="w-full bg-red-600 border border-transparent rounded-md py-3 px-4 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                    Basismitgliedschaft kündigen
                </button>
            </div>
        </div>
    @endif

    {{-- No Subscription Message --}}
    @if (!$pharmacy->subscribed() && !$cancellation)
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Keine aktive Mitgliedschaft</h3>
                <p class="mt-1 text-sm text-gray-500">
                    Sie haben derzeit keine aktive Basismitgliedschaft, die gekündigt werden kann.
                </p>
            </div>
        </div>
    @endif

    {{-- Cancellation Modal --}}
    @if ($showCancellationModal)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeCancellationModal"></div>
                
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Basismitgliedschaft kündigen
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        Sind Sie sicher, dass Sie Ihre Basismitgliedschaft kündigen möchten? 
                                        Die Kündigung wird zum {{ \App\SubscriptionCancellation::calculateNextQuarterEnd()->format('d.m.Y') }} wirksam.
                                    </p>
                                </div>

                                <div class="mt-4">
                                    <label for="cancellation-reason" class="block text-sm font-medium text-gray-700">
                                        Grund (optional)
                                    </label>
                                    <textarea 
                                        wire:model="cancellationReason"
                                        id="cancellation-reason" 
                                        rows="3" 
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                                        placeholder="Warum möchten Sie kündigen?"
                                    ></textarea>
                                </div>

                                <div class="mt-4">
                                    <label class="flex items-start">
                                        <input 
                                            wire:model="confirmCancellation"
                                            type="checkbox" 
                                            class="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">
                                            Ich bestätige, dass ich meine Basismitgliedschaft zum {{ \App\SubscriptionCancellation::calculateNextQuarterEnd()->format('d.m.Y') }} kündigen möchte. 
                                            Mir ist bewusst, dass nach der Kündigung keine neuen Features aktiviert werden können.
                                        </span>
                                    </label>
                                    @error('confirmCancellation')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                @error('cancellation')
                                    <div class="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
                                        <p class="text-sm text-red-600">{{ $message }}</p>
                                    </div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button 
                            wire:click="requestCancellation"
                            type="button" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Kündigung bestätigen
                        </button>
                        <button 
                            wire:click="closeCancellationModal"
                            type="button" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Abbrechen
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Revoke Modal --}}
    @if ($showRevokeModal)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeRevokeModal"></div>
                
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Kündigung widerrufen
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        Möchten Sie Ihre Kündigung widerrufen? Ihre Basismitgliedschaft wird dann fortgesetzt.
                                    </p>
                                </div>

                                <div class="mt-4">
                                    <label for="revoke-reason" class="block text-sm font-medium text-gray-700">
                                        Grund (optional)
                                    </label>
                                    <textarea 
                                        wire:model="revokeReason"
                                        id="revoke-reason" 
                                        rows="3" 
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        placeholder="Warum möchten Sie die Kündigung widerrufen?"
                                    ></textarea>
                                </div>

                                <div class="mt-4">
                                    <label class="flex items-start">
                                        <input 
                                            wire:model="confirmRevoke"
                                            type="checkbox" 
                                            class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">
                                            Ich bestätige, dass ich meine Kündigung widerrufen möchte und meine Basismitgliedschaft fortsetzen will.
                                        </span>
                                    </label>
                                    @error('confirmRevoke')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                @error('revoke')
                                    <div class="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
                                        <p class="text-sm text-red-600">{{ $message }}</p>
                                    </div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button 
                            wire:click="revokeCancellation"
                            type="button" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Widerruf bestätigen
                        </button>
                        <button 
                            wire:click="closeRevokeModal"
                            type="button" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Abbrechen
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
