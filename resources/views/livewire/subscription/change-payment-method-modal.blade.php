<flux:modal name="change-payment-method" class="w-1/2 space-y-6">
    <div class="border border-gray-300 rounded-lg p-2 w-min">
        <flux:icon.credit-card class="size-6"/>
    </div>

    <p class="font-semibold">Zahlungsmethode ändern</p>

    <div>
        <div class="mt-3 space-y-2">
            <x-subscription.select-box :selected="$selectedPaymentMethod === \App\Enums\PaymentMethod::SEPA" wire:click="switchPaymentMethod('{{ \App\Enums\PaymentMethod::SEPA }}')">
                {{ \App\Enums\PaymentMethod::SEPA->label() }}
                <x-slot name="icon">
                    <x-svg-icon iconId="sepa" class="size-8"/>
                </x-slot>
                <x-slot name="badge">
                    Empfohlen
                </x-slot>
                <x-slot name="description">
                    @if (!is_null($sepaData))
                        <p class="text-gray-500 text-sm">{{ $sepaData->name }}</p>
                        <p class="text-gray-500 text-sm">{{ \Illuminate\Support\Str::pan($sepaData->iban) }}</p>
                    @endif

                    <flux:modal.trigger name="sepa-modal">
                        <x-button type="button" size="xs" appearance="outline" class="mt-3 font-semibold text-brand-600 text-sm cursor-pointer">
                            @if ($pharmacy->sepaExists())
                                SEPA-Lastschriftmandat ändern
                            @else
                                SEPA-Lastschriftmandat erteilen
                            @endif
                        </x-button>
                    </flux:modal.trigger>
                </x-slot>
            </x-subscription.select-box>

            <x-subscription.select-box :selected="$selectedPaymentMethod === \App\Enums\PaymentMethod::Invoice" wire:click="switchPaymentMethod('{{ \App\Enums\PaymentMethod::Invoice }}')">
                {{ \App\Enums\PaymentMethod::Invoice->label() }}
                <x-slot name="icon">
                    <x-svg-icon iconId="pay-later" class="size-8"/>
                </x-slot>
            </x-subscription.select-box>

            @error('selectedPaymentMethod')
            <div class="text-red-500 text-sm mt-2">{{ $message }}</div>
            @enderror
        </div>

        <div class="mt-4 flex items-center justify-end gap-2" x-data="{ buttonDisabled: false }">
            <x-button appearance="outline" type="button" wire:click="closeModal">Abbrechen</x-button>
            <x-button appearance="brand"
                      x-on:click="$wire.save; buttonDisabled = true;"
                      x-bind:disabled="buttonDisabled">
                Zahlungsmethode ändern
            </x-button>
        </div>
    </div>

</flux:modal>
