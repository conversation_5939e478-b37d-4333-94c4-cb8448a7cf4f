@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                @lang('kim.show_title')
            </x-slot>
            <x-slot name="section">
                {{ $kimAddress->email }}
            </x-slot>
            <x-slot name="description">
                @lang('kim.show_description')
            </x-slot>
            @if (user()->can('delete', $kimAddress))
                <x-slot name="action">
                    <x:modal name="delete-kim-address">
                        <x-slot name="activator">
                            <x:button>
                                <svg class="-ml-1 mr-2 h-5 w-5"><use href="/icons.svg#trash"/></svg>
                                @lang('messages.cancelSubscription')
                            </x:button>
                        </x-slot>
                        <div>
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <svg class="h-6 w-6 text-red-600"><use href="/icons.svg#trash"/></svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-5">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-headline">
                                    @lang('kim.delete_title')
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm leading-5 text-gray-500">
                                        @lang('kim.delete_description')
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-5 sm:mt-6 flex flex-wrap">
                            <div class="w-1/2 px-1">
                                <x:button x-on:click="open = false" appearance="secondary" wrapperClass="w-full" class="w-full">
                                    @lang('messages.abort')
                                </x:button>
                            </div>
                            <div class="w-1/2 px-1">
                                <form method="POST" action="{{ route('kims.destroy', [$pharmacy, $kimAddress]) }}" class="w-full">
                                    @csrf
                                    @method('DELETE')
                                    <x:button type="submit" appearance="primary" wrapperClass="w-full" class="w-full">
                                        @lang('messages.cancelSubscription')
                                    </x:button>
                                </form>
                            </div>
                        </div>
                    </x:modal>
                </x-slot>
            @endif
        </x:header>

        {{-- Flash Notifications --}}
        @if (session('kim_created_successfully'))
            <div class="mb-8">
                @if(session('kim_created_successfully') == 'ordered')
                    <x:alert
                        :title="__('kim.register.success_title')"
                        :description="__('kim.register.success_description')"
                        type="success"
                    />
                @else
                    <x:alert
                        :title="__('kim.reservation.success_title')"
                        :description="__('kim.reservation.success_description')"
                        type="success"
                    />
                @endif
            </div>
        @endif

        {{-- Create warning --}}
        @if($kimAddress->status == \App\Enums\KimAddressStatus::RESERVED->value)
            <x-alert class="mb-8" type="warning" :description="__('kim.create_warning_no_change')"/>
        @endif

        {{-- Detail card --}}
        <x-kim.details-card :kim-address="$kimAddress" />

        {{-- Next steps --}}
        @if($kimAddress->status == \App\Enums\KimAddressStatus::RESERVED->value && \App\Helper\KimAddressHelper::bookingAvailable($pharmacy))
            <x-dynamic-component :component="'kim.next-steps.' . strtolower(\App\Helper\KimAddressHelper::getVendor($pharmacy))" :kim-address="$kimAddress" :pharmacy="$pharmacy"/>

            {{-- Booking form--}}
            <livewire:is :component="'kim-address.' . strtolower(\App\Helper\KimAddressHelper::getVendor($pharmacy)) . '.order-form'" :pharmacy="$pharmacy" :kim-address="$kimAddress"/>
        @elseif($kimAddress->status === \App\Enums\KimAddressStatus::ORDERED->value)
            <x-kim-address.appointment-booking-card :kim-address="$kimAddress"/>
        @endif
    </x:content>
@endsection
