<nav class="fixed left-0 top-0 z-40 w-full bg-white shadow">
    @if (showTermsOfUseHeaderBanner())
        @include('layouts.header-banner-terms-of-use')
    @endif

    <div class="px-2 sm:px-4 lg:px-8">
        <div class="relative flex h-16 items-center justify-between">
            <!-- Logo section -->
            <div class="flex items-center px-2 lg:px-0 xl:w-72">
                <a
                    class="flex flex-shrink-0 items-center"
                    href="{{ route('home') }}"
                >
                    @include('layouts.app.navbar-side-components.gedisa-logo')
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="flex lg:hidden">
		            <x-notification-center.badge/>

                <button
                    class="inline-flex items-center justify-center rounded-md p-2 text-gray-500 transition duration-150 ease-in-out focus:bg-gray-100 focus:text-gray-600 focus:outline-none"
                    aria-label="Main menu"
                    x-on:click="sidebarOpen = true;"
                    x-bind:aria-label="sidebarOpen ? 'Close main menu' : 'Main menu'"
                    x-bind:aria-expanded="sidebarOpen"
                >
                    <svg class="block h-6 w-6">
                        <use href="/icons.svg#bars-3-center-left" />
                    </svg>
                </button>
            </div>

            <!-- Links section -->
            <div class="hidden lg:block lg:w-80">
                <div class="flex items-center justify-end">
                    <x-notification-center.badge/>

                    <!-- Profile dropdown -->
                    <x-profileDropdown />
                </div>
            </div>
        </div>
    </div>

    @yield('subnavbar')
</nav>
