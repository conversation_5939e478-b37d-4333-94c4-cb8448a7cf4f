<?php

namespace Database\Factories;

use App\AccountingCenter;
use App\Enums\AssociationEnum;
use App\Enums\PharmacyStatusEnum;
use App\GoodsManagementSystem;
use App\Pharmacy;
use Illuminate\Database\Eloquent\Factories\Factory;

class PharmacyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Pharmacy::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $goodsManagementSystemId = GoodsManagementSystem::first()->id;
        $accountingCenterId = AccountingCenter::first()->id;

        return [
            'name' => $this->faker->pharmacyName,
            'institute_id' => rand(*********, ********9),
            'n_id' => $this->faker->nId,
            'pharmacy_id' => strval(rand(********, ********)),
            'phone' => $this->faker->phoneNumber,
            'fax' => $this->faker->phoneNumber,
            'email' => $this->faker->email,
            'website' => 'https://www.'.$this->faker->domainName,
            'courier_service' => true,
            'courier_service_radius' => 5.2,
            'has_near_parking_space' => ! rand(0, 1),
            // TODO: images ?
            'active' => true,
            'verification_status' => PharmacyStatusEnum::VERIFIED,
            'goods_management_system_id' => $goodsManagementSystemId,
            'accounting_center_id' => $accountingCenterId,
            'export_added_value_to_gematik' => true,
            'show_in_apoguide' => true,
            'uses_sdr' => false,
            'uses_apomail' => false,
            'send_invoices' => true,
        ];
    }

    public function pendingVerification()
    {
        return $this->state(function (array $attributes) {
            return [
                'verification_status' => PharmacyStatusEnum::PENDING,
            ];
        });
    }

    public function associationWl(): self
    {
        return $this->state(fn () => ['association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V]);
    }

    public function withNId($value)
    {
        return $this->state(function (array $attributes) use ($value) {
            return [
                'n_id' => $value,
            ];
        });
    }
}
