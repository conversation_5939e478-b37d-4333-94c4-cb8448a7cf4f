<?php

use App\Domains\Subscription\Application\Settings\Products\BaseProductSetting;
use App\Domains\Subscription\Application\Settings\Products\CalendarProductSetting;
use App\Domains\Subscription\Application\Settings\Products\CardLinkPacketMPlusProductSetting;
use App\Domains\Subscription\Application\Settings\Products\CardLinkPacketMProductSetting;
use App\Domains\Subscription\Application\Settings\Products\CardLinkProductSetting;
use App\Domains\Subscription\Application\Settings\Products\IAProductSetting;
use App\Domains\Subscription\Application\Settings\Products\KimProductSetting;
use App\Domains\Subscription\Application\Settings\Products\SDRProductSetting;
use App\Domains\Subscription\Application\Settings\Products\ShiftPlanProductSetting;
use App\Domains\Subscription\Application\Settings\Products\TimProductSetting;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add(CardLinkProductSetting::group().'.product_id', 'prod_RQAAqtw1pgJzth');
        $this->migrator->add(CardLinkProductSetting::group().'.recurring_price_id', 'price_1QXJpiE7mrGmlRDB3wZX1WdI');
        $this->migrator->add(CardLinkProductSetting::group().'.one_time_price_id', 'price_1QXJq7E7mrGmlRDBmqpUvjUc');

        $this->migrator->add(CalendarProductSetting::group().'.product_id', 'prod_RQAEHA5ukFuKZP');
        $this->migrator->add(CalendarProductSetting::group().'.recurring_price_id', 'price_1QXJuME7mrGmlRDBMzyCzNKl');
        $this->migrator->add(CalendarProductSetting::group().'.one_time_price_id', 'price_1QXJucE7mrGmlRDBAzg0DB9e');

        $this->migrator->add(BaseProductSetting::group().'.product_id', 'prod_RQAGs4PDYQO3zY');
        $this->migrator->add(BaseProductSetting::group().'.recurring_price_id', 'price_1QXJvqE7mrGmlRDBmCUCcIcW');
        $this->migrator->add(BaseProductSetting::group().'.one_time_price_id', 'price_1QXJvaE7mrGmlRDBkiIgqtVJ');

        $this->migrator->add(IAProductSetting::group().'.product_id', 'prod_RQAHHuweUnNEBa');
        $this->migrator->add(IAProductSetting::group().'.recurring_price_id', 'price_1QXJxDE7mrGmlRDBucVfTyHU');
        $this->migrator->add(IAProductSetting::group().'.one_time_price_id', 'price_1QXJxPE7mrGmlRDBp18qc2uE');

        $this->migrator->add(ShiftPlanProductSetting::group().'.product_id', 'prod_RQAJZ65g0tU3jT');
        $this->migrator->add(ShiftPlanProductSetting::group().'.recurring_price_id', 'price_1QXJyjE7mrGmlRDBCwhXQYQN');
        $this->migrator->add(ShiftPlanProductSetting::group().'.one_time_price_id', 'price_1QXJz2E7mrGmlRDBM5vm8DqX');

        $this->migrator->add(TimProductSetting::group().'.product_id', 'prod_RQAKZTkX4eY38u');
        $this->migrator->add(TimProductSetting::group().'.recurring_price_id', 'price_1QXJzzE7mrGmlRDBRULwwkhm');
        $this->migrator->add(TimProductSetting::group().'.one_time_price_id', 'price_1QXK0CE7mrGmlRDBU1zQxGIZ');

        $this->migrator->add(KimProductSetting::group().'.product_id', 'prod_RXg5FJsEABXFfk');
        $this->migrator->add(KimProductSetting::group().'.recurring_price_id', 'price_1QeajQE7mrGmlRDBkAfastli');
        $this->migrator->add(KimProductSetting::group().'.one_time_price_id', 'price_1QealEE7mrGmlRDBduHyWmyi');

        $this->migrator->add(CardLinkPacketMProductSetting::group().'.product_id', 'prod_RYU5RzsIXh2e7c');
        $this->migrator->add(CardLinkPacketMProductSetting::group().'.recurring_price_id', '');
        $this->migrator->add(CardLinkPacketMProductSetting::group().'.one_time_price_id', 'price_1QfN7XE7mrGmlRDBUIn0YAHn');

        $this->migrator->add(CardLinkPacketMPlusProductSetting::group().'.product_id', 'prod_RYU7MtDUill78g');
        $this->migrator->add(CardLinkPacketMPlusProductSetting::group().'.recurring_price_id', '');
        $this->migrator->add(CardLinkPacketMPlusProductSetting::group().'.one_time_price_id', 'price_1QfN9XE7mrGmlRDBiy2CYSML');

        $this->migrator->add(SDRProductSetting::group().'.product_id', 'prod_RZuQcGHl3XUyWw');
        $this->migrator->add(SDRProductSetting::group().'.recurring_price_id', 'price_1QgkbtE7mrGmlRDB1NYKJ6J7');
        $this->migrator->add(SDRProductSetting::group().'.one_time_price_id', 'price_1QgkcWE7mrGmlRDBepePpRyA');
    }
};
