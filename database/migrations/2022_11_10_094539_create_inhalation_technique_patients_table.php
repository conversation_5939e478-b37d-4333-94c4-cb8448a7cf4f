<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInhalationTechniquePatientsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inhalation_technique_patients', function (Blueprint $table) {
            $table->unsignedBigInteger('ps_id')->primary();

            $table->string('pzn_number')->nullable();
            $table->text('device_condition')->nullable();
            $table->text('device_condition_optional_text')->nullable();
            $table->text('preparation')->nullable();
            $table->text('preparation_optional_text')->nullable();
            $table->text('inhalation')->nullable();
            $table->text('inhalation_optional_text')->nullable();
            $table->text('ending')->nullable();
            $table->text('ending_optional_text')->nullable();

            $table->timestamps();

            $table->foreign('ps_id')
                ->references('id')
                ->on('pharmaceutical_services')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inhalation_technique_patients');
    }
}
