<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInfluenzaVaccinationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('influenza_vaccinations', function (Blueprint $table) {
            $table->unsignedBigInteger('vaccination_id')->primary();

            $table->unsignedTinyInteger('stiko_health_related')->nullable();
            $table->unsignedTinyInteger('stiko_job_related')->nullable();

            $table->json('severe_vaccination_reactions')->nullable();
            $table->tinyInteger('emergency_measures_initiated')->nullable();

            $table->json('poll_vaccinated_before')->nullable();
            $table->json('poll_where_found_out')->nullable();
            $table->string('poll_where_found_out_other')->nullable();
            $table->tinyInteger('poll_had_alternative')->nullable();
            $table->json('poll_why_pharmacy')->nullable();
            $table->string('poll_why_pharmacy_other')->nullable();
            $table->tinyInteger('poll_rate_information')->nullable();
            $table->tinyInteger('poll_rate_satisfaction')->nullable();
            $table->tinyInteger('poll_rate_do_again')->nullable();
            $table->tinyInteger('poll_rate_others_too')->nullable();
            $table->text('comments')->nullable();
            $table->boolean('notify_user')->default(false);

            $table->boolean('patient_has_illness')->nullable();
            $table->boolean('patient_has_illness_vaccination_possible')->nullable();
            $table->boolean('patient_has_allergy')->nullable();
            $table->boolean('patient_has_allergy_vaccination_possible')->nullable();
            $table->boolean('patient_had_reaction')->nullable();
            $table->boolean('patient_had_reaction_vaccination_possible')->nullable();
            $table->boolean('patient_has_operation')->nullable();
            $table->boolean('patient_has_operation_vaccination_possible')->nullable();
            $table->boolean('patient_takes_marcumar')->nullable();
            $table->boolean('patient_is_pregnant')->nullable();

            $table->boolean('patient_no_further_questions')->default(false);
            $table->boolean('patient_consent')->default(false);
            $table->boolean('patient_not_consent')->default(false);

            $table->boolean('vaccine_correct_color')->nullable();
            $table->boolean('vaccine_free_of_particles')->nullable();

            $table->text('allergy')->nullable();

            $table->timestamps();

            $table->foreign('vaccination_id')
                ->references('id')
                ->on('vaccinations')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('influenza_vaccinations');
    }
}
