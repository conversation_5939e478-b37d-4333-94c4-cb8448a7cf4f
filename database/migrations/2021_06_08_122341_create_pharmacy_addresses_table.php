<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Symfony\Component\Console\Output\ConsoleOutput;

class CreatePharmacyAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pharmacy_addresses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pharmacy_id');
            $table->unsignedTinyInteger('type');

            $table->string('street');
            $table->string('house_number');
            $table->string('postcode');
            $table->string('city');

            $table->float('latitude', 9, 6)->nullable()->default(null);
            $table->float('longitude', 9, 6)->nullable()->default(null);

            $table->timestamps();

            $table->foreign('pharmacy_id')
                ->references('id')
                ->on('pharmacies')
                ->onDelete('cascade');
        });

        try {
            Artisan::call('pharmacies:migrate-addresses');
        } catch (Exception $exception) {
            (new ConsoleOutput)->writeln('<error>'.$exception->getMessage().'</error>');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pharmacy_addresses');
    }
}
