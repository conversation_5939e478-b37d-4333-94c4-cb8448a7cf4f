<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class InsertInitialIntoFocusAreasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('focus_areas')->whereIn('name', [
            'lorem',
            'ipsum',
        ])->delete();

        Schema::table('focus_areas', function (Blueprint $table) {
            $table->string('name', 256)->change();
        });

        DB::table('focus_areas')->insert([
            [
                'name' => 'supply_with_cytostatics_and_parenteralia',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'opioid_substitution',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('focus_areas')->whereIn('name', [
            'supply_with_cytostatics_and_parenteralia',
            'opioid_substitution',
        ])->delete();

        Schema::table('focus_areas', function (Blueprint $table) {
            $table->string('name', 16)->change();
        });

        DB::table('focus_areas')->insert([
            [
                'name' => 'lorem',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'ipsum',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }
}
