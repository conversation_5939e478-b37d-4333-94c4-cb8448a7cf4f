<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_change_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Wer die Änderung vorgenommen hat
            $table->string('action'); // 'cancellation_requested', 'cancellation_confirmed', 'cancellation_revoked', 'cancellation_executed'
            $table->json('old_data')->nullable(); // Vorherige Daten
            $table->json('new_data')->nullable(); // Neue Daten
            $table->text('notes')->nullable(); // Zusätzliche Notizen
            $table->string('source')->default('web'); // 'web', 'nova', 'api', 'system'
            $table->timestamps();
            
            $table->index(['pharmacy_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_change_history');
    }
};
