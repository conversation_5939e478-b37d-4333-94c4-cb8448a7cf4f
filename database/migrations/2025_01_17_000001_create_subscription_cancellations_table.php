<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subscription_cancellations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pharmacy_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Wer die Kündigung ausgesprochen hat
            $table->enum('status', ['pending', 'confirmed', 'revoked', 'executed'])->default('pending');
            $table->timestamp('requested_at'); // Wann wurde die Kündigung beantragt
            $table->timestamp('effective_date'); // Wann wird die Kündigung wirksam (Quartalsende)
            $table->timestamp('confirmed_at')->nullable(); // Wann wurde die Kündigung bestätigt
            $table->timestamp('revoked_at')->nullable(); // Wann wurde die Kündigung widerrufen
            $table->timestamp('executed_at')->nullable(); // Wann wurde die Kündigung ausgeführt
            $table->text('reason')->nullable(); // Grund für die Kündigung (optional)
            $table->json('metadata')->nullable(); // Zusätzliche Daten (z.B. welche Features betroffen sind)
            $table->timestamps();
            
            // Nur eine aktive Kündigung pro Apotheke
            $table->unique(['pharmacy_id'], 'unique_active_cancellation');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_cancellations');
    }
};
