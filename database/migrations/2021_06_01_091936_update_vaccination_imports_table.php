<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateVaccinationImportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('vaccination_imports');

        Schema::create('vaccination_imports', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('pharmacy_id')->nullable();

            $table->string('search_string');
            $table->string('search_token');

            $table->unsignedTinyInteger('accounting_type');

            $table->timestamps();

            $table->foreign('pharmacy_id')
                ->references('id')
                ->on('pharmacies')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vaccination_imports');

        Schema::create('vaccination_imports', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('pharmacy_id')->nullable();

            $table->string('fn_last_name');
            $table->string('gn_first_name');
            $table->string('dob_birthdate');

            $table->string('id_telematics_id');
            $table->string('tg_vaccine_target');
            $table->string('vp_vaccine_type');
            $table->string('mp_vaccine_name');
            $table->string('ma_vaccine_holder');
            $table->unsignedInteger('dn_dose_number');
            $table->unsignedInteger('sd_total_dose_series');
            $table->date('dt_vaccination_date');

            $table->string('token')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('pharmacy_id')
                ->references('id')
                ->on('pharmacies')
                ->onDelete('set null');
        });
    }
}
