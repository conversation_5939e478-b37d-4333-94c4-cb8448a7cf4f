<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewIndexOnVaccinationImportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->index(['accounting_type', 'is_recovered']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vaccination_imports', function (Blueprint $table) {
            $table->dropIndex(['accounting_type', 'is_recovered']);
        });
    }
}
