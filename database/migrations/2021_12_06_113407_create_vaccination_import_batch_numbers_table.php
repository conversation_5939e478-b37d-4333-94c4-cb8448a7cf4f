<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVaccinationImportBatchNumbersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vaccination_import_batch_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('vaccine_name');
            $table->string('vaccine_holder');
            $table->date('expiration_date');
            $table->date('valid_from');
            $table->string('batch_number')->index();
            $table->string('pnr');
            $table->string('registration_number');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vaccination_import_batch_numbers');
    }
}
