<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFocusAreaPharmacyTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('focus_area_pharmacy', function (Blueprint $table) {
            $table->timestamps();

            $table->unsignedBigInteger('pharmacy_id');
            $table->unsignedBigInteger('focus_area_id');

            $table->primary(['pharmacy_id', 'focus_area_id']);

            $table->foreign('pharmacy_id')
                ->references('id')
                ->on('pharmacies')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('focus_area_pharmacy');
    }
}
