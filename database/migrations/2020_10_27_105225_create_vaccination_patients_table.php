<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVaccinationPatientsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vaccination_patients', function (Blueprint $table) {
            $table->unsignedBigInteger('vaccination_id')->primary();

            $table->text('birthdate')->nullable();
            $table->text('first_name')->nullable();
            $table->text('last_name')->nullable();
            $table->text('insurance_number')->nullable();
            $table->text('street')->nullable();
            $table->text('house_number')->nullable();
            $table->text('postcode')->nullable();
            $table->text('city')->nullable();
            $table->text('email')->nullable();
            $table->text('phone')->nullable();
            $table->string('gender')->nullable();

            $table->timestamps();

            $table->foreign('vaccination_id')
                ->references('id')
                ->on('vaccinations')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vaccination_patients');
    }
}
