<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('feature_recommendations', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_id')->nullable(false);
            $table->foreignId('feature_id')->nullable(false);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('feature_recommendations');
    }
};
