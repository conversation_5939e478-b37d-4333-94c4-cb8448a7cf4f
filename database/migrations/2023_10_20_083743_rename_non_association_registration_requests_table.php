<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('non_association_registration_requests', function (Blueprint $table) {
                $table->dropUnique(['email']);
                $table->dropForeign(['staff_id']);
            });
        }
        Schema::rename('non_association_registration_requests', 'registration_requests');

        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('registration_requests', function (Blueprint $table) {
                $table->unique(['email']);
                $table->foreign('staff_id')->nullable()->references('id')->on('staff')->nullOnDelete();
            });
        }

        Schema::table('brochure_codes', function (Blueprint $table) {
            if (DB::getDriverName() !== 'sqlite') {
                $table->dropForeign(['non_association_registration_request_id']);
            }

            $table->renameColumn('non_association_registration_request_id', 'registration_request_id');

            if (DB::getDriverName() !== 'sqlite') {
                $table->foreign('registration_request_id')->nullable()->references('id')->on('registration_requests')->cascadeOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('registration_requests', function (Blueprint $table) {
                $table->dropUnique(['email']);
                $table->dropForeign(['staff_id']);
            });
        }
        Schema::rename('registration_requests', 'non_association_registration_requests');

        if (DB::getDriverName() !== 'sqlite') {
            Schema::table('non_association_registration_requests', function (Blueprint $table) {
                $table->unique(['email']);
                $table->foreign('staff_id')->nullable()->references('id')->on('staff')->nullOnDelete();
            });
        }

        Schema::table('brochure_codes', function (Blueprint $table) {
            if (DB::getDriverName() !== 'sqlite') {
                $table->dropForeign(['registration_request_id']);
            }

            $table->renameColumn('registration_request_id', 'non_association_registration_request_id');

            if (DB::getDriverName() !== 'sqlite') {
                $table->foreign('non_association_registration_request_id')->nullable()->references('id')->on('non_association_registration_requests')->cascadeOnDelete();
            }
        });
    }
};
