<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \App\MeasureBloodPressurePatient::query()
            ->whereHas('pharmaceuticalService', function ($query) {
                $query->where('status', \App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus::FINISHED);
            })->delete();

        \App\InhalationTechniquePatient::query()
            ->whereHas('pharmaceuticalService', function ($query) {
                $query->where('status', \App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus::FINISHED);
            })->delete();

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pharmacies', function (Blueprint $table) {
            $table->dropColumn('send_invoices');
        });
    }
};
