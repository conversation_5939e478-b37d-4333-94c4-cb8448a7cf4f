<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('influenza_vaccinations', function (Blueprint $table) {
            $table->boolean('patient_takes_marcumar_vaccination_possible')->nullable();
            $table->boolean('patient_is_pregnant_vaccination_possible')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('influenza_vaccinations', function (Blueprint $table) {
            $table->dropColumn(['patient_takes_marcumar_vaccination_possible', 'patient_is_pregnant_vaccination_possible']);
        });
    }
};
