<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class InsertInitialIntoAccountingCentersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('accounting_centers')->insert([
            [
                'company' => 'Abrechnungszentrum HSB Schrader',
                'system_id' => 'AZ0001',
                'tag' => 'HSB',
                'city' => 'Brakel',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apotheken- und Ärzte-Abrechnungszentrum Dr. Güldener GmbH',
                'system_id' => 'AZ0002',
                'tag' => 'AÄA',
                'city' => 'Stuttgart',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apotheken-Rechen-Zentrum GmbH',
                'system_id' => 'AZ0003',
                'tag' => 'ARD',
                'city' => 'Darmstadt',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apothekenrechenzentrun Wünsch GmbH',
                'system_id' => 'AZ0004',
                'tag' => 'ARW',
                'city' => 'Breitingen / Werra',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'ARZ Service GmbH',
                'system_id' => 'AZ0005',
                'tag' => 'ARZ',
                'city' => 'Haan',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apotheken-Verrechnungs- und Codierstelle Dick GmbH & Co. KG',
                'system_id' => 'AZ0006',
                'tag' => 'AVD',
                'city' => 'Bernkastel-Kues',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Apotheken Verrechnungsstelle Helmut Weil',
                'system_id' => 'AZ0007',
                'tag' => 'AVW',
                'city' => '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'AVN Apotheken-Verrechnungsstelle Dr. Carl Carstes GmbH & Co. KG',
                'system_id' => 'AZ0008',
                'tag' => 'AVN',
                'city' => 'Bremen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Digitales Rezept Zentrum GmbH',
                'system_id' => 'AZ0009',
                'tag' => 'DRZ',
                'city' => 'Starnberg',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'GfAL - Gesellschaft für Apothekenabrechnung und Leistungserbringer GmbH',
                'system_id' => 'AZ0010',
                'tag' => 'GAL',
                'city' => 'Berlin',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Norddeutsches Apotheken-Rechenzentrum e. V. (NARZ e. V.)',
                'system_id' => 'AZ0011',
                'tag' => 'NAR',
                'city' => 'Bremen',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Rechenzentrum für Apotheken Hildegard Schröter GmbH',
                'system_id' => 'AZ0012',
                'tag' => 'RHS',
                'city' => 'Lutherstadt Wittenberg',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Rechenzentrum für Berliner Apotheken Stein & Reichwald GmbH',
                'system_id' => 'AZ0013',
                'tag' => 'RSW',
                'city' => 'Neuenhagen b. Berlin',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'company' => 'Rezeptabrechnungsstelle Berliner Apotheker GmbH',
                'system_id' => 'AZ0014',
                'tag' => 'RBA',
                'city' => 'Berlin',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('accounting_centers')->whereIn('system_id', [
            'AZ0001',
            'AZ0002',
            'AZ0003',
            'AZ0004',
            'AZ0005',
            'AZ0006',
            'AZ0007',
            'AZ0008',
            'AZ0009',
            'AZ0010',
            'AZ0011',
            'AZ0012',
            'AZ0013',
            'AZ0014',
        ])->delete();
    }
}
