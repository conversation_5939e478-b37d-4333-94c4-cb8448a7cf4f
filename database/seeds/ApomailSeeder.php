<?php

namespace Database\Seeders;

use App\Apomail;
use App\User;
use Illuminate\Database\Seeder;

class ApomailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::onlyOwner()->get();

        $users->each(function ($user) {
            Apomail::factory()->count(5)->create([
                'owner_id' => $user->id,
            ]);
        });

        $users = User::all();

        Apomail::all()->each(function ($apomail) use ($users) {
            $usersFromOwner = $users->filter(function (User $user) use ($apomail) {
                return $user->owner() && $apomail->owner_id === $user->owner()->id;
            });
            if ($usersFromOwner->count()) {
                $apomail->users()->attach(
                    $users->random(1)->pluck('id')->toArray()
                );
            }
        });
    }
}
